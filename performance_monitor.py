import time
import psutil
import json
import os
from datetime import datetime
from PyQt5.QtCore import QTimer, QObject, pyqtSignal
from config_manager import ConfigManager

class PerformanceMonitor(QObject):
    """性能监控器 - 监控系统资源使用情况"""
    
    # 信号定义
    memory_warning = pyqtSignal(float)  # 内存警告
    cpu_warning = pyqtSignal(float)     # CPU警告
    performance_report = pyqtSignal(dict)  # 性能报告
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.config_manager = ConfigManager()
        self.performance_config = self.config_manager.get_config('performance_config')
        
        # 监控配置
        self.memory_limit = self.performance_config.get('memory_limit', 512)  # MB
        self.cpu_limit = 80.0  # CPU使用率限制
        self.monitor_interval = 30000  # 30秒监控一次
        
        # 性能数据存储
        self.performance_history = []
        self.max_history_size = 100
        
        # 日志文件
        self.log_file = "performance.log"
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.monitor_performance)
        
        # 报告定时器
        self.report_timer = QTimer()
        self.report_timer.timeout.connect(self.generate_performance_report)
        
        print("✓ 性能监控器初始化完成")
    
    def start_monitoring(self):
        """开始性能监控"""
        self.monitor_timer.start(self.monitor_interval)
        self.report_timer.start(300000)  # 5分钟生成一次报告
        self.log_message("性能监控已启动")
        print("✓ 性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitor_timer.stop()
        self.report_timer.stop()
        self.log_message("性能监控已停止")
        print("✓ 性能监控已停止")
    
    def monitor_performance(self):
        """监控系统性能"""
        try:
            # 获取当前进程信息
            process = psutil.Process()
            
            # 内存使用情况
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            memory_percent = process.memory_percent()
            
            # CPU使用情况
            cpu_percent = process.cpu_percent()
            
            # 系统整体情况
            system_memory = psutil.virtual_memory()
            system_cpu = psutil.cpu_percent()
            
            # 创建性能数据
            perf_data = {
                'timestamp': datetime.now().isoformat(),
                'process_memory_mb': round(memory_mb, 2),
                'process_memory_percent': round(memory_percent, 2),
                'process_cpu_percent': round(cpu_percent, 2),
                'system_memory_percent': round(system_memory.percent, 2),
                'system_cpu_percent': round(system_cpu, 2),
                'available_memory_mb': round(system_memory.available / 1024 / 1024, 2)
            }
            
            # 添加到历史记录
            self.performance_history.append(perf_data)
            if len(self.performance_history) > self.max_history_size:
                self.performance_history.pop(0)
            
            # 检查警告条件
            if memory_mb > self.memory_limit:
                self.memory_warning.emit(memory_mb)
                self.log_message(f"内存使用警告: {memory_mb:.1f}MB > {self.memory_limit}MB")
            
            if cpu_percent > self.cpu_limit:
                self.cpu_warning.emit(cpu_percent)
                self.log_message(f"CPU使用警告: {cpu_percent:.1f}% > {self.cpu_limit}%")
            
            # 详细日志（仅在调试模式下）
            if self.performance_config.get('debug_logging', False):
                self.log_message(f"性能监控: 内存={memory_mb:.1f}MB, CPU={cpu_percent:.1f}%")
            
        except Exception as e:
            self.log_message(f"性能监控异常: {e}")
    
    def generate_performance_report(self):
        """生成性能报告"""
        try:
            if not self.performance_history:
                return
            
            # 计算统计数据
            memory_values = [data['process_memory_mb'] for data in self.performance_history]
            cpu_values = [data['process_cpu_percent'] for data in self.performance_history]
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'monitoring_period_minutes': len(self.performance_history) * (self.monitor_interval / 60000),
                'memory_stats': {
                    'current_mb': memory_values[-1] if memory_values else 0,
                    'average_mb': round(sum(memory_values) / len(memory_values), 2),
                    'max_mb': max(memory_values),
                    'min_mb': min(memory_values)
                },
                'cpu_stats': {
                    'current_percent': cpu_values[-1] if cpu_values else 0,
                    'average_percent': round(sum(cpu_values) / len(cpu_values), 2),
                    'max_percent': max(cpu_values),
                    'min_percent': min(cpu_values)
                },
                'warnings': {
                    'memory_warnings': len([m for m in memory_values if m > self.memory_limit]),
                    'cpu_warnings': len([c for c in cpu_values if c > self.cpu_limit])
                }
            }
            
            # 发送报告信号
            self.performance_report.emit(report)
            
            # 保存报告到文件
            self.save_performance_report(report)
            
            # 记录日志
            self.log_message(f"性能报告: 平均内存={report['memory_stats']['average_mb']:.1f}MB, "
                           f"平均CPU={report['cpu_stats']['average_percent']:.1f}%")
            
        except Exception as e:
            self.log_message(f"生成性能报告异常: {e}")
    
    def save_performance_report(self, report):
        """保存性能报告到文件"""
        try:
            report_file = f"performance_report_{datetime.now().strftime('%Y%m%d')}.json"
            
            # 读取现有报告
            reports = []
            if os.path.exists(report_file):
                try:
                    with open(report_file, 'r', encoding='utf-8') as f:
                        reports = json.load(f)
                except:
                    reports = []
            
            # 添加新报告
            reports.append(report)
            
            # 保持最近50个报告
            if len(reports) > 50:
                reports = reports[-50:]
            
            # 保存到文件
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(reports, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.log_message(f"保存性能报告失败: {e}")
    
    def log_message(self, message):
        """记录日志消息"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = f"[{timestamp}] {message}\n"
            
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
            # 保持日志文件大小
            self.rotate_log_file()
            
        except Exception as e:
            print(f"日志记录失败: {e}")
    
    def rotate_log_file(self):
        """轮转日志文件"""
        try:
            if os.path.exists(self.log_file):
                file_size = os.path.getsize(self.log_file)
                max_size = 1024 * 1024  # 1MB
                
                if file_size > max_size:
                    # 备份当前日志
                    backup_file = f"{self.log_file}.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    os.rename(self.log_file, backup_file)
                    
                    # 清理旧备份（保留最近5个）
                    import glob
                    backup_files = sorted(glob.glob(f"{self.log_file}.*"))
                    if len(backup_files) > 5:
                        for old_backup in backup_files[:-5]:
                            try:
                                os.remove(old_backup)
                            except:
                                pass
                                
        except Exception as e:
            print(f"日志轮转失败: {e}")
    
    def get_current_performance(self):
        """获取当前性能数据"""
        if self.performance_history:
            return self.performance_history[-1]
        return None
    
    def get_performance_summary(self):
        """获取性能摘要"""
        if not self.performance_history:
            return None
        
        memory_values = [data['process_memory_mb'] for data in self.performance_history]
        cpu_values = [data['process_cpu_percent'] for data in self.performance_history]
        
        return {
            'current_memory_mb': memory_values[-1],
            'average_memory_mb': sum(memory_values) / len(memory_values),
            'current_cpu_percent': cpu_values[-1],
            'average_cpu_percent': sum(cpu_values) / len(cpu_values),
            'data_points': len(self.performance_history)
        }
