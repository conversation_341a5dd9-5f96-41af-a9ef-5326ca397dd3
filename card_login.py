import sys
import json
import hashlib
import uuid
import platform
import psutil
import os
import requests
import time
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime

# 在创建QApplication之前设置WebEngine所需的属性
def setup_webengine():
    """设置WebEngine所需的属性"""
    try:
        from PyQt5.QtCore import QCoreApplication
        QCoreApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
        print("WebEngine属性设置成功")
    except Exception as e:
        print(f"WebEngine属性设置失败: {e}")

# 在文件开头就设置WebEngine属性
setup_webengine()

class FloatingLabelLineEdit(QLineEdit):
    """Material Design风格的浮动标签输入框"""
    def __init__(self, label_text, parent=None):
        super().__init__(parent)
        self.label_text = label_text
        self.label_color = QColor(108, 117, 125)  # 增强对比度
        self.label_color_focused = QColor(102, 126, 234)
        self.is_focused = False
        self.is_filled = False
        
        self.setStyleSheet("""
            QLineEdit {
                border: none;
                border-bottom: 2px solid #dee2e6;
                padding: 15px 0px 8px 0px;
                font-size: 16px;
                background: transparent;
                color: #212529;
            }
            QLineEdit:focus {
                border-bottom: 2px solid #667eea;
                outline: none;
            }
        """)
        
        self.textChanged.connect(self.on_text_changed)
        
    def focusInEvent(self, event):
        self.is_focused = True
        self.update()
        super().focusInEvent(event)
        
    def focusOutEvent(self, event):
        self.is_focused = False
        self.update()
        super().focusOutEvent(event)
        
    def on_text_changed(self, text):
        self.is_filled = bool(text)
        self.update()
        
    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制浮动标签
        if self.is_focused or self.is_filled:
            # 标签上浮状态
            painter.setPen(self.label_color_focused if self.is_focused else self.label_color)
            font = QFont()
            font.setPointSize(12)
            painter.setFont(font)
            painter.drawText(0, -5, self.label_text)
        else:
            # 标签占位状态
            painter.setPen(self.label_color)
            font = QFont()
            font.setPointSize(16)
            painter.setFont(font)
            painter.drawText(0, 20, self.label_text)

class MaterialButton(QPushButton):
    """Material Design风格按钮"""
    def __init__(self, text, primary=True, parent=None):
        super().__init__(text, parent)
        self.primary = primary
        self.setup_style()
        
    def setup_style(self):
        if self.primary:
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #667eea, stop:1 #764ba2);
                    color: white;
                    border: none;
                    border-radius: 25px;
                    padding: 12px 24px;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #5a6fd8, stop:1 #6a4190);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4e60c6, stop:1 #5e377e);
                }
                QPushButton:disabled {
                    background: #cccccc;
                    color: #666666;
                }
            """)
        else:
            # 关闭按钮 - 减小圆角，增强文字对比度
            self.setStyleSheet("""
                QPushButton {
                    background: transparent;
                    color: #495057;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background: #f8f9fa;
                    border-color: #adb5bd;
                    color: #343a40;
                }
                QPushButton:pressed {
                    background: #e9ecef;
                    border-color: #6c757d;
                }
            """)

class CardLoginWindow(QMainWindow):
    """现代化Material Design卡密登录窗口"""
    login_success = pyqtSignal(dict)  # 登录成功信号
    
    def __init__(self):
        self.is_php_format = True
        super().__init__()
        self.setWindowTitle("卡密验证 - 登录")
        self.setFixedSize(400, 650)  # 增加窗口高度
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 配置文件路径
        self.config_file = "card_login_config.json"
        
        # 初始化健康证系统认证管理器
        self.health_auth = HealthAuthManager()
        
        # 居中显示
        self.center_window()
        
        # 初始化组件
        self.setup_ui()
        self.setup_connections()
        
        # 加载设备信息
        self.load_device_info()
        
        # 加载保存的卡密
        self.load_saved_card_key()
        
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        
    def setup_ui(self):
        """设置UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 背景设为透明
        central_widget.setStyleSheet("""
            QWidget {
                background: transparent;
            }
        """)
        
        # 卡片容器 - 增加高度
        card_widget = QWidget()
        card_widget.setFixedSize(350, 580)  # 增加卡片高度
        card_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 20px;
                border: none;
            }
        """)
        
        # 卡片阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        card_widget.setGraphicsEffect(shadow)
        
        # 卡片布局 - 调整间距
        card_layout = QVBoxLayout(card_widget)
        card_layout.setContentsMargins(40, 30, 40, 30)  # 减少上下边距
        card_layout.setSpacing(20)  # 减少组件间距
        
        # 右上角关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #6c757d;
                border: none;
                border-radius: 15px;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #f8f9fa;
                color: #495057;
            }
            QPushButton:pressed {
                background: #e9ecef;
            }
        """)
        close_btn.clicked.connect(self.close)
        
        # 创建顶部布局用于放置关闭按钮
        top_layout = QHBoxLayout()
        top_layout.addStretch()
        top_layout.addWidget(close_btn)
        top_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.addLayout(top_layout)
        
        # 标题
        title_label = QLabel("卡密验证")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        card_layout.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("请输入您的卡密进行验证")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                margin-bottom: 15px;
            }
        """)
        card_layout.addWidget(subtitle_label)
        
        # 卡密输入框
        self.card_key_input = FloatingLabelLineEdit("卡密")
        self.card_key_input.setEchoMode(QLineEdit.Password)
        # 设置密码字符为横线
        self.card_key_input.setInputMethodHints(Qt.ImhHiddenText)
        card_layout.addWidget(self.card_key_input)
        
        # 设备ID显示
        device_container = QWidget()
        device_layout = QVBoxLayout(device_container)
        device_layout.setContentsMargins(0, 0, 0, 0)
        device_layout.setSpacing(5)
        
        device_label = QLabel("设备ID")
        device_label.setStyleSheet("""
            QLabel {
                font-size: 12px; 
                color: #2c3e50; 
                margin-bottom: 5px;
                font-weight: 600;
            }
        """)
        device_layout.addWidget(device_label)
        
        self.device_id_label = QLabel("正在生成...")
        self.device_id_label.setStyleSheet("""
            QLabel {
                background: #f8f9fa;
                padding: 12px;
                border-radius: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                color: #495057;
                border: 1px solid #e9ecef;
                word-wrap: break-word;
            }
        """)
        self.device_id_label.setWordWrap(True)
        device_layout.addWidget(self.device_id_label)
        
        card_layout.addWidget(device_container)
        
        # 登录按钮
        self.login_btn = MaterialButton("验证登录", primary=True)
        self.login_btn.setFixedHeight(50)
        card_layout.addWidget(self.login_btn)
        
        # 记住卡密复选框
        self.remember_checkbox = QCheckBox("记住卡密")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                color: #495057;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #dee2e6;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #667eea;
                border-color: #667eea;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QCheckBox::indicator:hover {
                border-color: #667eea;
            }
        """)
        card_layout.addWidget(self.remember_checkbox)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                text-align: center;
                font-size: 12px;
                color: #495057;
                background: #f8f9fa;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 6px;
            }
        """)
        card_layout.addWidget(self.progress_bar)

        # 状态显示 - 确保有足够空间
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                padding: 10px;
                border-radius: 8px;
                margin-top: 10px;
                color: #495057;
                min-height: 40px;
            }
        """)
        self.status_label.setWordWrap(True)  # 允许文字换行
        card_layout.addWidget(self.status_label)
        
        # 添加少量弹性空间
        card_layout.addStretch(1)
        
        # 添加弹性空间使卡片居中
        main_layout.addStretch()
        main_layout.addWidget(card_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        
    def setup_connections(self):
        """设置信号连接"""
        self.login_btn.clicked.connect(self.perform_login)
        self.card_key_input.returnPressed.connect(self.perform_login)
        
    def load_device_info(self):
        """加载设备信息"""
        from device_manager import DeviceManager
        
        self.device_manager = DeviceManager()
        device_id = self.device_manager.get_formatted_device_id()
        self.device_id_label.setText(device_id)
        
    def perform_login(self):
        """执行登录验证 - 带进度可视化"""
        card_key = self.card_key_input.text().strip()

        if not card_key:
            self.show_status("请输入卡密", "error")
            return

        # 禁用登录按钮并显示进度条
        self.login_btn.setEnabled(False)
        self.login_btn.setText("验证中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # 创建认证管理器
        from card_auth import CardAuthManager

        self.auth_manager = CardAuthManager()
        self.auth_manager.auth_success.connect(self.on_auth_success)
        self.auth_manager.auth_failed.connect(self.on_auth_failed)
        self.auth_manager.auth_progress.connect(self.on_auth_progress)

        # 设置认证参数
        device_id = self.device_manager.get_device_id()
        self.auth_manager.set_credentials(card_key, device_id)

        # 开始认证并更新进度
        self.update_progress(10, "正在验证卡密...")
        self.auth_manager.start()

    def update_progress(self, value, message):
        """更新进度条和状态"""
        self.progress_bar.setValue(value)
        self.show_status(message, "info")
        QApplication.processEvents()  # 强制更新UI
    
    def on_auth_success(self, result):
        """认证成功回调 - 带进度更新"""
        self.update_progress(50, "卡密验证成功！正在启动系统...")
        self.login_btn.setText("启动中...")

        # 保存卡密配置
        self.save_card_key_config()

        # 直接启动主程序，跳过健康证系统认证
        self.update_progress(90, "准备启动健康证管理系统...")
        self.start_main_system_directly(result)

    def on_auth_failed(self, error_msg):
        """认证失败回调"""
        self.progress_bar.setVisible(False)
        self.show_status(error_msg, "error")
        self.login_btn.setEnabled(True)
        self.login_btn.setText("验证登录")

    def on_auth_progress(self, message):
        """认证进度回调 - 带进度更新"""
        if "连接服务器" in message:
            self.update_progress(20, message)
        elif "验证卡密" in message:
            self.update_progress(30, message)
        elif "处理响应" in message:
            self.update_progress(40, message)
        else:
            self.show_status(message, "info")
    
    def show_status(self, message, status_type="info"):
        """显示状态信息"""
        colors = {
            "success": "#d4edda",
            "error": "#f8d7da", 
            "info": "#d1ecf1"
        }
        
        text_colors = {
            "success": "#155724",
            "error": "#721c24",
            "info": "#0c5460"
        }
        
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                background: {colors.get(status_type, colors["info"])};
                color: {text_colors.get(status_type, text_colors["info"])};
                font-size: 14px;
                padding: 10px;
                border-radius: 8px;
                margin-top: 10px;
                border: 1px solid {colors.get(status_type, colors["info"])};
            }}
        """)

    def load_saved_card_key(self):
        """加载保存的卡密"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    saved_card_key = config.get('card_key', '')
                    remember_checked = config.get('remember_card_key', False)
                    
                    if remember_checked and saved_card_key:
                        self.card_key_input.setText(saved_card_key)
                        self.remember_checkbox.setChecked(True)
        except Exception as e:
            print(f"加载卡密配置失败: {e}")

    def save_card_key_config(self):
        """保存卡密配置"""
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            if self.remember_checkbox.isChecked():
                config['card_key'] = self.card_key_input.text()
                config['remember_card_key'] = True
            else:
                config['card_key'] = ''
                config['remember_card_key'] = False
            
            config['last_updated'] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存卡密配置失败: {e}")

    def start_main_system_directly(self, card_result):
        """直接启动主程序，跳过健康证系统认证"""
        try:
            self.update_progress(95, "正在启动健康证管理系统...")

            # 准备传递给主程序的数据
            login_data = {
                'card_auth': card_result,
                'health_auth': {
                    'access_token': 'mock_token',  # 使用模拟token
                    'refresh_token': 'mock_refresh_token',
                    'expires_at': time.time() + 3600
                },
                'login_time': datetime.now().isoformat(),
                'direct_start': True,  # 标记为直接启动
                'card_info': self.extract_card_info(card_result)
            }

            # 延迟发送登录成功信号
            QTimer.singleShot(500, lambda: self.update_progress(100, "启动成功！"))
            QTimer.singleShot(1000, lambda: self.login_success.emit(login_data))

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.show_status(f"启动失败: {str(e)}", "error")
            self.login_btn.setEnabled(True)
            self.login_btn.setText("验证登录")

    def login_health_system(self, card_result):
        """登录健康证系统 - 并行优化版"""
        try:
            self.show_status("正在连接健康证系统...", "info")

            # 创建并行认证线程
            from PyQt5.QtCore import QThread, pyqtSignal

            class ParallelHealthAuth(QThread):
                auth_completed = pyqtSignal(bool, str, dict)

                def __init__(self, health_auth):
                    super().__init__()
                    self.health_auth = health_auth

                def run(self):
                    try:
                        success, message = self.health_auth.login_api()
                        auth_data = self.health_auth.get_auth_data() if success else {}
                        self.auth_completed.emit(success, message, auth_data)
                    except Exception as e:
                        self.auth_completed.emit(False, f"认证异常: {str(e)}", {})

            # 启动并行认证
            self.parallel_auth = ParallelHealthAuth(self.health_auth)
            self.parallel_auth.auth_completed.connect(self.on_parallel_auth_completed)
            self.parallel_auth.start()

            # 保存卡密认证结果，等待健康证认证完成
            self.pending_card_result = card_result

        except Exception as e:
            self.show_status(f"系统连接失败: {str(e)}", "error")
            self.login_btn.setEnabled(True)
            self.login_btn.setText("验证登录")

    def on_parallel_auth_completed(self, success, message, auth_data):
        """并行认证完成回调 - 带进度更新"""
        try:
            if success:
                self.update_progress(90, "健康证系统认证成功！")
                QTimer.singleShot(500, lambda: self.update_progress(100, "登录成功！正在启动系统..."))

                # 准备传递给主程序的数据
                login_data = {
                    'card_auth': self.pending_card_result,
                    'health_auth': auth_data,
                    'login_time': datetime.now().isoformat(),
                    'parallel_auth': True,  # 标记为并行认证
                    'card_info': self.extract_card_info(self.pending_card_result)  # 提取卡密详细信息
                }

                # 延迟发送登录成功信号，让用户看到完成进度
                QTimer.singleShot(1000, lambda: self.login_success.emit(login_data))

            else:
                self.progress_bar.setVisible(False)
                self.show_status(f"健康证系统登录失败: {message}", "error")
                self.login_btn.setEnabled(True)
                self.login_btn.setText("验证登录")

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.show_status(f"认证处理失败: {str(e)}", "error")
            self.login_btn.setEnabled(True)
            self.login_btn.setText("验证登录")

    def extract_card_info(self, card_result):
        """提取卡密详细信息"""
        try:
            if not card_result or not isinstance(card_result, dict):
                return {}

            data = card_result.get('data', {})
            if not data:
                return {}

            # 提取关键信息
            card_info = {
                'card_key': data.get('card_key', ''),
                'card_type': data.get('card_type', 'unknown'),
                'status': data.get('status', 0),
                'use_time': data.get('use_time', ''),
                'expire_time': data.get('expire_time', ''),
                'duration': data.get('duration', 0),
                'remaining_count': data.get('remaining_count', 0),
                'total_count': data.get('total_count', 0),
                'allow_reverify': data.get('allow_reverify', 0),
                'message': card_result.get('message', '验证成功'),
                'extracted_at': datetime.now().isoformat()
            }

            print(f"✓ 卡密信息提取完成:")
            print(f"  卡密类型: {card_info['card_type']}")
            if card_info['card_type'] == 'time':
                print(f"  到期时间: {card_info['expire_time']}")
            elif card_info['card_type'] == 'count':
                print(f"  剩余次数: {card_info['remaining_count']}/{card_info['total_count']}")

            return card_info

        except Exception as e:
            print(f"提取卡密信息失败: {e}")
            return {}

class HealthAuthManager:
    """健康证系统认证管理器"""
    def __init__(self):
        self.is_php_format = False  # 使用RESTful API端点
        self.base_url = "http://**************/api/v1"  # 修正IP地址
        self.auth_login_url = f"{self.base_url}/auth/login"
        self.access_token = None
        self.refresh_token = None
        self.expires_at = 0
    
    def login_api(self, username="8354724", password="8354724"):
        """调用健康证系统登录API获取令牌"""
        try:
            url = self.auth_login_url if not self.is_php_format else f"{self.base_url}/index.php?action=login"
            print(f"健康证系统登录URL: {url}")

            data = {
                "username": username,
                "password": password
            }
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "HealthCert-CardLogin/1.0"
            }

            print(f"发送登录请求: {username}")
            response = requests.post(url, json=data, headers=headers, timeout=10)

            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")  # 只显示前200字符

            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"解析的JSON: {result}")

                    if result.get("code") == 200:
                        token_data = result.get("data", {})
                        self.access_token = token_data.get("access_token")
                        self.refresh_token = token_data.get("refresh_token")
                        self.expires_at = time.time() + token_data.get("expires_in", 3600)
                        print("健康证系统认证成功")
                        return True, "健康证系统认证成功"
                    else:
                        error_msg = result.get("message", "健康证系统登录失败")
                        print(f"登录失败: {error_msg}")
                        return False, error_msg
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    print(f"原始响应: {response.text}")
                    return False, f"响应格式错误: {str(e)}"
            else:
                print(f"HTTP错误: {response.status_code}")
                return False, f"HTTP错误: {response.status_code}"

        except requests.exceptions.RequestException as e:
            print(f"网络请求错误: {e}")
            return False, f"健康证系统网络错误: {str(e)}"
        except Exception as e:
            print(f"未知错误: {e}")
            import traceback
            traceback.print_exc()
            return False, f"健康证系统错误: {str(e)}"
    
    def get_auth_data(self):
        """获取认证数据"""
        return {
            'access_token': self.access_token,
            'refresh_token': self.refresh_token,
            'expires_at': self.expires_at
        }

class AppManager:
    """应用程序管理器，统一管理登录窗口和主窗口"""
    def __init__(self):
        self.login_window = None
        self.main_window = None
        
    def show_login_window(self):
        """显示登录窗口"""
        self.login_window = CardLoginWindow()
        self.login_window.login_success.connect(self.on_login_success)
        self.login_window.show()
        return self.login_window
    
    def on_login_success(self, login_data):
        """登录成功回调 - 优化临时文件管理"""
        print("=" * 50)
        print("卡密验证成功！正在启动健康证管理系统...")
        print("=" * 50)

        # 更新登录窗口状态
        if self.login_window:
            self.login_window.show_status("卡密验证成功！正在启动健康证管理系统...", "success")

        # 优化的临时文件管理
        self.save_auth_data_secure(login_data)

        # 延迟启动主程序，给用户看到成功消息的时间
        QTimer.singleShot(2000, self.load_main_system)

    def save_auth_data_secure(self, login_data):
        """安全保存认证数据"""
        temp_auth_file = "temp_auth.json"
        backup_file = "temp_auth.backup.json"

        try:
            # 添加时间戳和安全标记
            secure_data = {
                **login_data,
                'saved_at': datetime.now().isoformat(),
                'expires_at': (datetime.now().timestamp() + 3600),  # 1小时后过期
                'process_id': os.getpid(),
                'secure_hash': self.generate_data_hash(login_data)
            }

            # 如果存在旧文件，先备份
            if os.path.exists(temp_auth_file):
                try:
                    import shutil
                    shutil.copy2(temp_auth_file, backup_file)
                except:
                    pass

            # 保存新的认证数据
            with open(temp_auth_file, 'w', encoding='utf-8') as f:
                json.dump(secure_data, f, indent=2, ensure_ascii=False)

            print(f"✓ 认证信息已安全保存到: {temp_auth_file}")

            # 设置定时清理
            QTimer.singleShot(3600000, self.cleanup_temp_files)  # 1小时后清理

        except Exception as e:
            print(f"✗ 保存认证信息失败: {e}")
            # 尝试使用备份文件
            if os.path.exists(backup_file):
                try:
                    import shutil
                    shutil.copy2(backup_file, temp_auth_file)
                    print("✓ 已恢复备份认证文件")
                except:
                    pass

    def generate_data_hash(self, data):
        """生成数据哈希值用于验证"""
        try:
            import hashlib
            data_str = json.dumps(data, sort_keys=True)
            return hashlib.md5(data_str.encode()).hexdigest()
        except:
            return "unknown"

    def cleanup_temp_files(self):
        """清理临时文件"""
        temp_files = ["temp_auth.json", "temp_auth.backup.json"]

        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    print(f"✓ 已清理临时文件: {temp_file}")
            except Exception as e:
                print(f"✗ 清理临时文件失败 {temp_file}: {e}")
    
    def load_main_system(self):
        """载入健康证管理系统"""
        try:
            print("正在载入健康证管理系统...")

            # 更新状态
            if self.login_window:
                self.login_window.show_status("正在载入健康证管理系统...", "info")

            # 定义可能的主程序路径（按优先级排序）
            possible_main_paths = [
                "main.py",           # 当前目录的main.py
                "mld/main.py",       # mld子目录的main.py
                os.path.join("mld", "main.py")  # 使用os.path.join确保路径正确
            ]

            main_module = None
            used_path = None

            # 尝试加载主程序模块
            import importlib.util
            for main_path in possible_main_paths:
                try:
                    print(f"尝试加载: {main_path}")

                    # 检查文件是否存在
                    if not os.path.exists(main_path):
                        print(f"  文件不存在: {main_path}")
                        continue

                    # 尝试动态导入
                    spec = importlib.util.spec_from_file_location("main", main_path)
                    if spec is None:
                        print(f"  无法创建模块规格: {main_path}")
                        continue

                    main_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(main_module)

                    # 检查是否有MainDemo类
                    if not hasattr(main_module, 'MainDemo'):
                        print(f"  模块中没有MainDemo类: {main_path}")
                        continue

                    used_path = main_path
                    print(f"✓ 成功加载主程序模块: {used_path}")
                    break

                except Exception as e:
                    print(f"  加载失败 {main_path}: {e}")
                    continue

            if main_module is None:
                raise ImportError("无法找到或加载有效的main.py文件。请检查以下路径是否存在且包含MainDemo类：" +
                                ", ".join(possible_main_paths))

            # 创建主窗口实例
            print("正在创建健康证管理系统窗口...")
            self.main_window = main_module.MainDemo()

            # 显示主窗口
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()

            # 强制刷新显示
            QApplication.instance().processEvents()

            print("✓ 健康证管理系统已启动")
            print(f"  使用的主程序: {used_path}")
            print(f"  窗口标题: {self.main_window.windowTitle()}")
            print(f"  窗口大小: {self.main_window.size()}")
            print(f"  窗口可见: {self.main_window.isVisible()}")

            # 延迟关闭登录窗口
            QTimer.singleShot(1000, self.close_login_window)

        except Exception as e:
            print(f"✗ 载入健康证管理系统失败: {e}")
            import traceback
            traceback.print_exc()

            # 显示错误信息给用户
            if self.login_window:
                error_msg = f"启动健康证管理系统失败: {str(e)}"
                self.login_window.show_status(error_msg, "error")
                self.login_window.login_btn.setEnabled(True)
                self.login_window.login_btn.setText("验证登录")
    
    def close_login_window(self):
        """关闭登录窗口"""
        if self.login_window:
            print("✓ 关闭卡密登录窗口")
            self.login_window.close()
            self.login_window = None

if __name__ == '__main__':
    print("健康证管理系统启动中...")
    
    # 确保WebEngine属性已设置
    setup_webengine()
    
    app = QApplication(sys.argv)
    
    # 设置应用程序名称
    app.setApplicationName("健康证管理系统")
    
    # 创建应用程序管理器
    app_manager = AppManager()
    
    # 显示登录窗口
    login_window = app_manager.show_login_window()
    
    # 确保窗口显示在前台
    login_window.raise_()
    login_window.activateWindow()
    
    print("登录窗口已显示")
    
    sys.exit(app.exec_())





