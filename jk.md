🔗 API基础地址： http://**************/api/v1/
📋 请求格式： 标准RESTful风格 (GET/POST/PUT/DELETE)
🔒 认证方式： JWT令牌认证 + 统一认证适配器
📄 数据格式： JSON (Content-Type: application/json)
🆕 API版本： v1.5 AuthAdapter已修复
✅ 系统状态： 全部功能正常 认证机制已优化
 最新修复 (2025-07-29)
✅ 修复了AuthAdapter类缺失的静态方法
✅ 解决了权限过滤函数调用问题
✅ 统一了JWT和传统会话认证机制
✅ 优化了中间件认证流程
✅ 改进了数据库连接管理
认证机制 已优化
API采用统一认证适配器(AuthAdapter)，支持JWT令牌和传统会话的混合认证：

JWT令牌认证
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
API Key认证
X-API-Key: hc_your_api_key_here
 认证适配器特性
🔄 自动检测认证方式（JWT/Session）
🛡️ 统一的权限检查机制
🔗 API和Web端无缝兼容
⚡ 高性能的用户信息缓存
响应格式
成功响应
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
错误响应
{
  "code": 400, // 或其他错误代码
  "message": "错误信息",
  "errors": {
    // 详细错误信息
  }
}
 API接口列表
🔐 认证接口 (Authentication)
接口名称	请求方法	接口地址	描述	认证要求
用户登录	POST	/auth/login	用户登录获取JWT双令牌	无需认证
刷新令牌	POST	/auth/refresh	使用刷新令牌获取新的访问令牌	需要刷新令牌
用户注销	POST	/auth/logout	用户注销，使当前令牌失效	需要认证
获取用户信息	GET	/auth/profile	获取当前用户信息	需要认证
修改密码	POST	/auth/change-password	修改当前用户密码	需要认证
📋 健康证接口 (Health Certificates)
📤 文件上传接口 (Upload)
⚙️ 系统管理接口 (System)
 API使用示例
JavaScript示例
// 登录获取令牌
fetch('http://**************/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'password123'
  })
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    const accessToken = data.data.access_token;
    
    // 使用令牌获取健康证列表
    fetch('http://**************/api/v1/certs', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    })
    .then(response => response.json())
    .then(certsData => {
      console.log('健康证列表', certsData);
    });
  }
});
PHP示例
// 登录获取令牌
$ch = curl_init('http://**************/api/v1/auth/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'username' => 'admin',
    'password' => 'password123'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

$response = curl_exec($ch);
curl_close($ch);

$data = json_decode($response, true);
if ($data['code'] === 200) {
    $accessToken = $data['data']['access_token'];
    
    // 使用令牌获取健康证列表
    $ch = curl_init('http://**************/api/v1/certs');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken
    ]);
    
    $certsResponse = curl_exec($ch);
    curl_close($ch);
    
    $certsData = json_decode($certsResponse, true);
    // 处理健康证数据
}