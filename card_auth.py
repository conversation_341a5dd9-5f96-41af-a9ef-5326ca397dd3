import requests
import json
from PyQt5.QtCore import QThread, pyqtSignal

class CardAuthManager(QThread):
    """卡密认证管理器"""
    
    # 信号定义
    auth_success = pyqtSignal(dict)  # 认证成功
    auth_failed = pyqtSignal(str)    # 认证失败
    auth_progress = pyqtSignal(str)  # 认证进度
    
    def __init__(self):
        super().__init__()
        self.api_url = "https://opne.xyz/api/verify.php"
        # 使用文档中的API密钥，如果不对请告知正确的密钥
        self.api_key = "323b0570bd90755222d9d5716c87aea6"
        self.card_key = ""
        self.device_id = ""

        # 增强的重试配置
        self.max_retries = 3
        self.retry_delays = [1, 2, 5]  # 重试间隔（秒）
        self.timeout_values = [10, 15, 20]  # 超时时间递增
        
    def set_credentials(self, card_key, device_id):
        """设置认证凭据"""
        self.card_key = card_key
        self.device_id = device_id
    
    def run(self):
        """执行认证请求 - 增强重试机制"""
        try:
            self.auth_progress.emit("正在连接服务器...")

            # 尝试多种方式和重试机制
            for attempt in range(self.max_retries):
                try:
                    self.auth_progress.emit(f"尝试连接 ({attempt + 1}/{self.max_retries})...")

                    # 首先尝试POST方式
                    success = self.try_post_request_with_retry(attempt)
                    if success:
                        return

                    # POST失败，尝试GET方式
                    self.auth_progress.emit("尝试备用连接方式...")
                    success = self.try_get_request_with_retry(attempt)
                    if success:
                        return

                    # 如果不是最后一次尝试，等待后重试
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                        self.auth_progress.emit(f"连接失败，{delay}秒后重试...")
                        import time
                        time.sleep(delay)

                except Exception as e:
                    print(f"认证尝试 {attempt + 1} 异常: {e}")
                    if attempt == self.max_retries - 1:
                        self.auth_failed.emit(f"认证过程发生错误: {str(e)}")
                        return

            self.auth_failed.emit("所有连接方式均失败，请检查网络或联系管理员")

        except Exception as e:
            self.auth_failed.emit(f"认证过程发生严重错误: {str(e)}")
    
    def try_post_request_with_retry(self, attempt):
        """尝试POST方式请求 - 带重试优化"""
        try:
            # 准备请求数据
            data = {
                'card_key': self.card_key,
                'device_id': self.device_id
            }

            # 准备请求头
            headers = {
                'Content-Type': 'application/json',
                'X-API-KEY': self.api_key,
                'User-Agent': 'CardAuth/1.0'
            }

            self.auth_progress.emit("正在验证卡密 (POST)...")

            # 使用递增的超时时间
            timeout = self.timeout_values[min(attempt, len(self.timeout_values) - 1)]

            # 发送POST请求
            response = requests.post(
                self.api_url,
                json=data,
                headers=headers,
                timeout=timeout
            )

            return self.handle_response(response, "POST")

        except requests.exceptions.Timeout:
            print(f"POST请求超时 (尝试 {attempt + 1})")
            return False
        except requests.exceptions.ConnectionError:
            print(f"POST连接错误 (尝试 {attempt + 1})")
            return False
        except Exception as e:
            print(f"POST请求异常 (尝试 {attempt + 1}): {e}")
            return False
    
    def try_get_request_with_retry(self, attempt):
        """尝试GET方式请求 - 带重试优化"""
        try:
            # 准备GET参数
            params = {
                'api_key': self.api_key,
                'card_key': self.card_key,
                'device_id': self.device_id
            }

            headers = {
                'User-Agent': 'CardAuth/1.0'
            }

            self.auth_progress.emit("正在验证卡密 (GET)...")

            # 使用递增的超时时间
            timeout = self.timeout_values[min(attempt, len(self.timeout_values) - 1)]

            # 发送GET请求
            response = requests.get(
                self.api_url,
                params=params,
                headers=headers,
                timeout=timeout
            )

            return self.handle_response(response, "GET")

        except requests.exceptions.Timeout:
            print(f"GET请求超时 (尝试 {attempt + 1})")
            if attempt == self.max_retries - 1:
                self.auth_failed.emit("连接超时，请检查网络连接")
            return False
        except requests.exceptions.ConnectionError:
            print(f"GET连接错误 (尝试 {attempt + 1})")
            if attempt == self.max_retries - 1:
                self.auth_failed.emit("无法连接到服务器，请检查网络")
            return False
        except Exception as e:
            print(f"GET请求异常 (尝试 {attempt + 1}): {e}")
            if attempt == self.max_retries - 1:
                self.auth_failed.emit(f"GET请求异常: {str(e)}")
            return False
    
    def handle_response(self, response, method):
        """处理响应结果"""
        try:
            print(f"{method} 响应状态码: {response.status_code}")
            print(f"{method} 响应内容: {response.text}")
            
            self.auth_progress.emit("正在处理响应...")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    error_code = result.get('code', -1)
                    
                    if error_code == 0:
                        # 认证成功
                        self.auth_success.emit(result)
                        return True
                    else:
                        # 认证失败，返回错误信息
                        error_msg = self.get_error_message(error_code, result.get('message', ''))
                        self.auth_failed.emit(error_msg)
                        return False
                except json.JSONDecodeError:
                    self.auth_failed.emit("服务器响应格式错误")
                    return False
            else:
                error_msg = f"网络错误: HTTP {response.status_code}"
                if response.status_code == 401:
                    error_msg += " - API密钥无效"
                elif response.status_code == 403:
                    error_msg += " - 访问被拒绝"
                elif response.status_code == 500:
                    error_msg += " - 服务器内部错误"
                
                print(f"{method} 请求失败: {error_msg}")
                return False
                
        except Exception as e:
            print(f"{method} 响应处理异常: {e}")
            return False
    
    def get_error_message(self, error_code, server_message=""):
        """根据错误码返回用户友好的错误信息"""
        error_messages = {
            0: "验证成功",
            1: "卡密不存在或已被其他设备使用",
            2: "API接口未启用，请联系管理员",
            3: "服务器内部错误，请稍后重试",
            4: "API密钥无效，请联系管理员",
            5: "卡密已被禁用，请联系管理员",
            6: "不允许重复验证，请联系管理员",
            7: "卡密次数已用完，请联系管理员"
        }
        
        base_message = error_messages.get(error_code, f"未知错误 (错误码: {error_code})")
        
        # 如果服务器返回了具体消息，优先使用服务器消息
        if server_message and server_message != base_message:
            return f"{base_message}: {server_message}"
        
        return base_message


