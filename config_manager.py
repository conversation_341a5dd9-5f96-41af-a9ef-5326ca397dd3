import json
import os
import time
from datetime import datetime
from typing import Dict, Any, Optional
import threading

class ConfigManager:
    """统一配置管理器 - 支持热重载和配置验证"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.configs = {}
        self.file_timestamps = {}
        self.default_configs = {
            'main_config': {
                'file': 'config.json',
                'defaults': {
                    'access_token': None,
                    'refresh_token': None,
                    'expires_at': 0,
                    'auto_refresh_threshold': 300,  # 提前5分钟刷新
                    'memory_cleanup_interval': 300000,  # 5分钟
                    'network_timeout': 30,
                    'max_retries': 3
                }
            },
            'card_login_config': {
                'file': 'card_login_config.json',
                'defaults': {
                    'card_key': '',
                    'remember_card_key': False,
                    'last_updated': None,
                    'auto_login': False,
                    'login_timeout': 15
                }
            },
            'device_config': {
                'file': 'device_config.json',
                'defaults': {
                    'device_id': None,
                    'generated_at': None,
                    'platform': None,
                    'hostname': None,
                    'cache_duration': 86400  # 24小时
                }
            },
            'performance_config': {
                'file': 'performance_config.json',
                'defaults': {
                    'webengine_cache_size': 50,  # MB
                    'max_tabs': 5,
                    'image_optimization': True,
                    'preload_enabled': True,
                    'memory_limit': 512,  # MB
                    'gc_interval': 60000  # 1分钟
                }
            },
            'server_config': {
                'file': 'server_config.json',
                'defaults': {
                    'server_host': 'localhost',
                    'api_version': 'v1',
                    'endpoints': {
                        'auth_login': '/api/v1/auth/login',
                        'auth_refresh': '/api/v1/auth/refresh',
                        'auth_logout': '/api/v1/auth/logout',
                        'auth_profile': '/api/v1/auth/profile',
                        'certs': '/api/v1/certs',
                        'cert_view': '/api/v1/certs/view',
                        'upload': '/api/v1/upload',
                        'upload_photo': '/api/v1/upload/photo',
                        'simple_api': '/api/v1/simple_api.php',
                        'standard_api': '/api/v1/'
                    },
                    'timeouts': {
                        'connection': 15,
                        'read': 30
                    },
                    'retry': {
                        'max_attempts': 3,
                        'delay': 1
                    },
                    'debug': True
                }
            }
        }
        
        # 初始化所有配置
        self.load_all_configs()
    
    def load_all_configs(self):
        """加载所有配置文件"""
        for config_name, config_info in self.default_configs.items():
            self.load_config(config_name)
    
    def load_config(self, config_name: str) -> Dict[str, Any]:
        """加载指定配置"""
        if config_name not in self.default_configs:
            raise ValueError(f"未知配置: {config_name}")
        
        config_info = self.default_configs[config_name]
        file_path = config_info['file']
        defaults = config_info['defaults']
        
        try:
            if os.path.exists(file_path):
                # 检查文件时间戳
                current_timestamp = os.path.getmtime(file_path)
                if (config_name in self.file_timestamps and 
                    self.file_timestamps[config_name] == current_timestamp):
                    # 文件未变化，返回缓存的配置
                    return self.configs.get(config_name, defaults.copy())
                
                # 加载配置文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # 合并默认配置和加载的配置
                config = defaults.copy()
                config.update(loaded_config)
                
                # 验证配置
                validated_config = self.validate_config(config_name, config)
                
                # 缓存配置和时间戳
                self.configs[config_name] = validated_config
                self.file_timestamps[config_name] = current_timestamp
                
                print(f"✓ 配置加载成功: {config_name} ({file_path})")
                return validated_config
            else:
                # 文件不存在，使用默认配置并创建文件
                config = defaults.copy()
                self.configs[config_name] = config
                self.save_config(config_name, config)
                print(f"✓ 创建默认配置: {config_name} ({file_path})")
                return config
                
        except Exception as e:
            print(f"✗ 加载配置失败 {config_name}: {e}")
            # 返回默认配置
            config = defaults.copy()
            self.configs[config_name] = config
            return config
    
    def save_config(self, config_name: str, config: Dict[str, Any]):
        """保存配置到文件"""
        if config_name not in self.default_configs:
            raise ValueError(f"未知配置: {config_name}")
        
        file_path = self.default_configs[config_name]['file']
        
        try:
            # 添加更新时间
            config_to_save = config.copy()
            config_to_save['last_updated'] = datetime.now().isoformat()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            
            # 更新缓存和时间戳
            self.configs[config_name] = config_to_save
            self.file_timestamps[config_name] = os.path.getmtime(file_path)
            
            print(f"✓ 配置保存成功: {config_name}")
            
        except Exception as e:
            print(f"✗ 保存配置失败 {config_name}: {e}")
    
    def get_config(self, config_name: str, key: str = None, default=None):
        """获取配置值"""
        # 检查是否需要重新加载（热重载）
        config = self.load_config(config_name)
        
        if key is None:
            return config
        
        return config.get(key, default)
    
    def set_config(self, config_name: str, key: str, value: Any):
        """设置配置值"""
        if config_name not in self.configs:
            self.load_config(config_name)
        
        self.configs[config_name][key] = value
        self.save_config(config_name, self.configs[config_name])
    
    def validate_config(self, config_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证和修复配置"""
        validated = config.copy()
        
        if config_name == 'main_config':
            # 验证令牌过期时间
            if validated.get('expires_at', 0) < time.time():
                validated['access_token'] = None
                validated['refresh_token'] = None
                validated['expires_at'] = 0
            
            # 验证数值范围
            validated['auto_refresh_threshold'] = max(60, min(1800, validated.get('auto_refresh_threshold', 300)))
            validated['network_timeout'] = max(5, min(60, validated.get('network_timeout', 30)))
            validated['max_retries'] = max(1, min(10, validated.get('max_retries', 3)))
        
        elif config_name == 'performance_config':
            # 验证性能参数
            validated['webengine_cache_size'] = max(10, min(200, validated.get('webengine_cache_size', 50)))
            validated['max_tabs'] = max(1, min(20, validated.get('max_tabs', 5)))
            validated['memory_limit'] = max(128, min(2048, validated.get('memory_limit', 512)))
            validated['gc_interval'] = max(30000, min(300000, validated.get('gc_interval', 60000)))
        
        return validated
    
    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        all_configs = {}
        for config_name in self.default_configs.keys():
            all_configs[config_name] = self.load_config(config_name)
        return all_configs
    
    def reload_all_configs(self):
        """重新加载所有配置"""
        print("重新加载所有配置...")
        self.file_timestamps.clear()
        self.load_all_configs()
        print("✓ 所有配置重新加载完成")
