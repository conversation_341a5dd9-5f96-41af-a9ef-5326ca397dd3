from PyQt5.QtCore import *
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import *
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
import sys
from datetime import datetime
import os
import requests
import json
import time


class AuthManager:
    def __init__(self):
        # 服务器配置
        self.server_host = self.get_server_host()  # 从配置文件或环境变量获取
        
        # 默认API端点
        self.api_base_url = f"http://{self.server_host}/api/v1"
        self.auth_login_url = f"{self.api_base_url}/auth/login"
        self.auth_refresh_url = f"{self.api_base_url}/auth/refresh"
        self.auth_logout_url = f"{self.api_base_url}/auth/logout"
        self.auth_profile_url = f"{self.api_base_url}/auth/profile"
        self.certs_url = f"{self.api_base_url}/certs"
        self.cert_view_url = f"{self.api_base_url}/certs/view"
        self.upload_url = f"{self.api_base_url}/upload"
        self.upload_photo_url = f"{self.api_base_url}/upload/photo"
        
        self.access_token = None
        self.refresh_token = None
        self.expires_at = 0
        self.config_file = "config.json"

        # 初始化默认参数
        self.auto_refresh_threshold = 300
        self.network_timeout = 30
        self.max_retries = 3

        # 尝试导入配置管理器
        try:
            from config_manager import ConfigManager
            self.config_manager = ConfigManager()

            # 更新性能优化参数
            self.auto_refresh_threshold = self.config_manager.get_config('main_config', 'auto_refresh_threshold', 300)
            self.network_timeout = self.config_manager.get_config('main_config', 'network_timeout', 30)
            self.max_retries = self.config_manager.get_config('main_config', 'max_retries', 3)

            # 从配置管理器获取并更新API端点
            server_config = self.config_manager.get_config('server_config') if hasattr(self.config_manager, 'get_config') else {}
            if server_config:
                # 更新超时设置
                if 'timeouts' in server_config:
                    self.network_timeout = server_config['timeouts'].get('connection', 15)
                
                # 更新API端点
                if 'endpoints' in server_config:
                    self.auth_login_url = f"http://{self.server_host}{server_config['endpoints'].get('auth_login', '/api/v1/auth/login')}"
                    self.auth_refresh_url = f"http://{self.server_host}{server_config['endpoints'].get('auth_refresh', '/api/v1/auth/refresh')}"
                    self.auth_logout_url = f"http://{self.server_host}{server_config['endpoints'].get('auth_logout', '/api/v1/auth/logout')}"
                    self.auth_profile_url = f"http://{self.server_host}{server_config['endpoints'].get('auth_profile', '/api/v1/auth/profile')}"
                    self.certs_url = f"http://{self.server_host}{server_config['endpoints'].get('certs', '/api/v1/certs')}"
                    self.cert_view_url = f"http://{self.server_host}{server_config['endpoints'].get('cert_view', '/api/v1/certs/view')}"
                    self.upload_url = f"http://{self.server_host}{server_config['endpoints'].get('upload', '/api/v1/upload')}"
                    self.upload_photo_url = f"http://{self.server_host}{server_config['endpoints'].get('upload_photo', '/api/v1/upload/photo')}"
                
                print(f"API基础地址: {self.api_base_url}")
                print(f"认证API: {self.auth_login_url}")
                print(f"健康证API: {self.certs_url}")
                print(f"上传API: {self.upload_url}, {self.upload_photo_url}")

        except Exception as e:
            print(f"配置管理器初始化失败，使用默认配置: {e}")
            self.config_manager = None

        # 令牌预刷新定时器
        from PyQt5.QtCore import QTimer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_token)

        self.load_config()
        
    def get_health_cert_host(self):
        """获取健康证服务器地址配置"""
        try:
            # 尝试从配置文件读取
            config_file = os.path.join(os.path.dirname(__file__), 'server_config.json')
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    host = config.get('health_cert_host', 'localhost')  # 默认使用localhost
                    print(f"从配置文件读取健康证服务器地址: {host}")
                    return host

            # 尝试从环境变量读取
            health_cert_host = os.environ.get('HEALTH_CERT_SERVER_HOST', 'localhost')
            print(f"从环境变量读取健康证服务器地址: {health_cert_host}")
            return health_cert_host

        except Exception as e:
            print(f"读取健康证服务器配置失败，使用默认地址: {e}")
            return 'localhost'  # 默认地址为localhost
            
    def get_server_host(self):
        """获取服务器地址配置"""
        try:
            # 尝试从配置文件读取
            config_file = os.path.join(os.path.dirname(__file__), 'server_config.json')
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    host = config.get('server_host', 'localhost')  # 默认使用localhost
                    print(f"从配置文件读取服务器地址: {host}")
                    return host

            # 尝试从环境变量读取
            server_host = os.environ.get('HEALTH_CERT_SERVER', 'localhost')  # 默认使用localhost
            print(f"从环境变量读取服务器地址: {server_host}")
            return server_host

        except Exception as e:
            print(f"读取服务器配置失败，使用默认地址: {e}")
            return 'localhost'  # 默认地址为localhost

    def login_api(self, username="8354724", password="8354724"):
        """调用登录API获取令牌 - 优化版"""
        # 检查是否有有效的缓存令牌
        if self.is_token_valid():
            print("✓ 使用缓存的有效令牌")
            return True, "使用缓存令牌认证成功"

        # 尝试使用刷新令牌
        if self.refresh_token and not self.is_token_valid():
            print("尝试使用刷新令牌...")
            refresh_success, refresh_msg = self.refresh_token_api()
            if refresh_success:
                return True, refresh_msg

        # 执行完整登录流程
        return self._perform_full_login(username, password)

    def _perform_full_login(self, username, password):
        """执行完整登录流程，支持重试机制"""
        for attempt in range(self.max_retries):
            try:
                print(f"登录尝试 {attempt + 1}/{self.max_retries}")

                url = self.auth_login_url  # 使用标准RESTful API端点
                data = {
                    "username": username,
                    "password": password
                }
                headers = {"Content-Type": "application/json"}

                print(f"发送登录请求到: {url}")
                print(f"请求数据: {data}")
                print(f"请求头: {headers}")

                response = requests.post(url, json=data, headers=headers, timeout=self.network_timeout)
                print(f"响应状态码: {response.status_code}")
                print(f"响应内容: {response.text[:500]}...")  # 显示前500字符

                if response.status_code != 200:
                    print(f"HTTP错误: {response.status_code}")
                    if attempt == self.max_retries - 1:
                        return False, f"HTTP错误: {response.status_code}"
                    time.sleep(1)
                    continue

                try:
                    result = response.json()
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    if attempt == self.max_retries - 1:
                        return False, f"响应格式错误: {str(e)}"
                    time.sleep(1)
                    continue

                if result.get("code") == 200:
                    token_data = result.get("data", {})
                    self.access_token = token_data.get("access_token")
                    self.refresh_token = token_data.get("refresh_token")
                    expires_in = token_data.get("expires_in", 3600)
                    self.expires_at = time.time() + expires_in

                    # 保存到配置管理器
                    self.save_config()

                    # 启动自动刷新定时器
                    self.start_auto_refresh_timer(expires_in)

                    print(f"✓ 登录成功，令牌有效期: {expires_in}秒")
                    return True, "认证成功"
                else:
                    error_msg = result.get("message", "登录失败")
                    if attempt == self.max_retries - 1:
                        return False, error_msg
                    print(f"登录失败: {error_msg}，准备重试...")
                    time.sleep(1)  # 重试前等待1秒

            except requests.exceptions.Timeout:
                if attempt == self.max_retries - 1:
                    return False, "网络超时，请检查网络连接"
                print(f"网络超时，准备重试...")
                time.sleep(2)
            except Exception as e:
                if attempt == self.max_retries - 1:
                    return False, f"网络错误: {str(e)}"
                print(f"网络错误: {e}，准备重试...")
                time.sleep(1)

        return False, "登录失败，已达到最大重试次数"
    
    def refresh_token_api(self):
        """刷新访问令牌"""
        if not self.refresh_token:
            return False, "无刷新令牌"
        
        try:
            url = self.auth_refresh_url  # 使用RESTful风格API
            data = {"refresh_token": self.refresh_token}
            headers = {"Content-Type": "application/json"}
            
            response = requests.post(url, json=data, headers=headers, timeout=10)
            result = response.json()
            
            if result.get("code") == 200:
                token_data = result.get("data", {})
                self.access_token = token_data.get("access_token")
                self.refresh_token = token_data.get("refresh_token")
                self.expires_at = time.time() + token_data.get("expires_in", 3600)
                self.save_config()
                return True, "令牌刷新成功"
            else:
                return False, result.get("message", "刷新失败")
        except Exception as e:
            return False, f"网络错误: {str(e)}"
    
    def start_auto_refresh_timer(self, expires_in):
        """启动自动刷新定时器"""
        # 计算提前刷新时间（提前5分钟或剩余时间的1/4，取较小值）
        refresh_advance = min(self.auto_refresh_threshold, expires_in // 4)
        refresh_delay = max(60, expires_in - refresh_advance) * 1000  # 转换为毫秒

        print(f"✓ 设置令牌自动刷新，将在 {refresh_delay//1000} 秒后刷新")
        self.refresh_timer.start(refresh_delay)

    def auto_refresh_token(self):
        """自动刷新令牌"""
        print("执行自动令牌刷新...")
        self.refresh_timer.stop()

        success, message = self.refresh_token_api()
        if success:
            print(f"✓ 自动刷新成功: {message}")
        else:
            print(f"✗ 自动刷新失败: {message}")
            # 刷新失败时，尝试重新登录
            self.login_api()

    def save_config(self):
        """保存配置到统一配置管理器"""
        try:
            # 保存到配置管理器（如果可用）
            if self.config_manager:
                self.config_manager.set_config('main_config', 'access_token', self.access_token)
                self.config_manager.set_config('main_config', 'refresh_token', self.refresh_token)
                self.config_manager.set_config('main_config', 'expires_at', self.expires_at)

            # 兼容性：同时保存到原始文件
            config = {
                "access_token": self.access_token,
                "refresh_token": self.refresh_token,
                "expires_at": self.expires_at
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)

        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def load_config(self):
        """从配置管理器加载配置"""
        try:
            # 从配置管理器加载（如果可用）
            if self.config_manager:
                main_config = self.config_manager.get_config('main_config')
                self.access_token = main_config.get("access_token")
                self.refresh_token = main_config.get("refresh_token")
                self.expires_at = main_config.get("expires_at", 0)
            else:
                # 回退到原始文件加载
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        self.access_token = config.get("access_token")
                        self.refresh_token = config.get("refresh_token")
                        self.expires_at = config.get("expires_at", 0)

            # 如果令牌有效且即将过期，启动自动刷新
            if self.is_token_valid():
                remaining_time = self.expires_at - time.time()
                if remaining_time > self.auto_refresh_threshold:
                    self.start_auto_refresh_timer(int(remaining_time))

            print(f"✓ 配置加载完成，令牌状态: {'有效' if self.is_token_valid() else '无效'}")

        except Exception as e:
            print(f"加载配置失败: {e}")
    
    def is_token_valid(self):
        """检查访问令牌是否有效"""
        if not self.access_token:
            print("Token检查: 无访问令牌")
            return False
        
        current_time = time.time()
        if current_time >= self.expires_at:
            print(f"Token检查: 令牌已过期 (当前时间: {current_time}, 过期时间: {self.expires_at})")
            return False
        
        print(f"Token检查: 令牌有效 (剩余时间: {self.expires_at - current_time}秒)")
        return True
    
    def get_auth_header(self):
        """获取认证头"""
        if not self.is_token_valid():
            print("获取认证头失败: Token无效")
            return None
        
        auth_header = {"Authorization": f"Bearer {self.access_token}"}
        print(f"认证头: {auth_header}")
        return auth_header

    def check_api_compatibility(self):
        """检查API兼容性，确保可以连接到服务器"""
        try:
            print(f"正在检查API兼容性: {self.api_base_url}")

            # 尝试连接API基础地址
            response = requests.get(f"{self.api_base_url}/", timeout=self.network_timeout)
            print(f"API基础地址检查状态码: {response.status_code}")

            if response.status_code < 400:
                print("✓ API连接正常")
                return True, "API连接正常"
            else:
                print(f"⚠ API连接返回错误状态码: {response.status_code}，但系统将继续运行")
                return True, f"API服务器响应异常，但系统可正常使用"  # 改为警告而非错误
        except Exception as e:
            print(f"⚠ API连接失败: {str(e)}，但系统将继续运行")
            return True, f"API连接失败，但系统可正常使用"  # 改为警告而非错误


class WebView(QWebEngineView):
    """自定义WebView类 - 性能优化版"""
    def __init__(self, parent=None):
        super().__init__(parent)

        # 导入配置管理器
        from config_manager import ConfigManager
        self.config_manager = ConfigManager()
        self.performance_config = self.config_manager.get_config('performance_config')

        # 性能监控
        self.load_start_time = 0
        self.memory_usage_timer = QTimer()
        self.memory_usage_timer.timeout.connect(self.monitor_memory)
        self.memory_usage_timer.start(self.performance_config.get('gc_interval', 60000))

        self.setup_page()

    def setup_page(self):
        """设置页面属性 - 性能优化版"""
        page = self.page()

        # 设置用户代理
        page.profile().setHttpUserAgent(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        )

        # 设置页面背景色
        page.setBackgroundColor(QColor(255, 255, 255))

        # 性能优化设置
        settings = page.settings()
        try:
            # 基础设置
            settings.setAttribute(QWebEngineSettings.ErrorPageEnabled, False)
            settings.setAttribute(QWebEngineSettings.FullScreenSupportEnabled, False)
            settings.setAttribute(QWebEngineSettings.JavascriptCanOpenWindows, False)
            settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)
            settings.setAttribute(QWebEngineSettings.LocalContentCanAccessRemoteUrls, True)

            # 性能优化设置
            settings.setAttribute(QWebEngineSettings.AcceleratedCompositingEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebGLEnabled, False)  # 禁用WebGL减少内存
            settings.setAttribute(QWebEngineSettings.PluginsEnabled, False)  # 禁用插件

            # 缓存设置
            cache_size = self.performance_config.get('webengine_cache_size', 50) * 1024 * 1024  # MB转字节
            page.profile().setHttpCacheMaximumSize(cache_size)

        except Exception as e:
            print(f"WebEngine设置失败: {e}")

        # 绑定加载事件
        self.loadStarted.connect(self.on_load_started)
        self.loadFinished.connect(self.on_load_finished)

    def on_load_started(self):
        """页面开始加载"""
        self.load_start_time = time.time()

    def on_load_finished(self, success):
        """页面加载完成"""
        if self.load_start_time > 0:
            load_time = time.time() - self.load_start_time
            print(f"页面加载耗时: {load_time:.2f}秒, 成功: {success}")

            if success and self.performance_config.get('image_optimization', True):
                # 延迟执行图片优化
                QTimer.singleShot(1000, self.optimize_images)

    def optimize_images(self):
        """优化页面图片"""
        try:
            self.page().runJavaScript("""
                // 图片懒加载和优化
                var images = document.querySelectorAll('img');
                images.forEach(function(img) {
                    // 设置图片渲染优化
                    img.style.imageRendering = 'auto';
                    img.style.imageRendering = 'crisp-edges';

                    // 限制图片最大尺寸
                    if (img.naturalWidth > 800) {
                        img.style.maxWidth = '800px';
                        img.style.height = 'auto';
                    }

                    // 添加加载完成事件
                    img.onload = function() {
                        this.style.opacity = '1';
                    };
                });

                // 移除不必要的元素
                var unnecessaryElements = document.querySelectorAll('script[src*="analytics"], script[src*="tracking"]');
                unnecessaryElements.forEach(function(el) {
                    el.remove();
                });
            """)
        except Exception as e:
            print(f"图片优化失败: {e}")

    def monitor_memory(self):
        """监控内存使用"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            memory_limit = self.performance_config.get('memory_limit', 512)

            if memory_mb > memory_limit:
                print(f"⚠ 内存使用过高: {memory_mb:.1f}MB > {memory_limit}MB，执行清理...")
                self.cleanup_memory()

        except Exception as e:
            print(f"内存监控失败: {e}")

    def cleanup_memory(self):
        """清理内存"""
        try:
            # JavaScript垃圾回收
            self.page().runJavaScript("""
                // 强制垃圾回收
                if (window.gc) window.gc();
                if (window.CollectGarbage) window.CollectGarbage();

                // 清理缓存
                if ('caches' in window) {
                    caches.keys().then(function(names) {
                        names.forEach(function(name) {
                            caches.delete(name);
                        });
                    });
                }
            """)

            # 清理HTTP缓存
            self.page().profile().clearHttpCache()

            print("✓ WebEngine内存清理完成")

        except Exception as e:
            print(f"内存清理失败: {e}")
    
    def createWindow(self, window_type):
        """处理新窗口请求"""
        # 在新标签页中打开
        print(f"创建新窗口类型: {window_type}")
        if hasattr(self.parent(), 'add_new_tab'):
            return self.parent().add_new_tab()
        return None


class MainDemo(QMainWindow):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        print("=" * 50)
        print("初始化健康证管理系统...")
        print("=" * 50)
        
        try:
            # 首先初始化配置管理器
            from config_manager import ConfigManager
            self.config_manager = ConfigManager()
            print("✓ 配置管理器初始化完成")

            # 初始化卡密信息存储
            self.card_info = {
                'card_key': None,
                'card_type': None,  # 'time' 或 'count'
                'status': 0,
                'use_time': None,
                'expire_time': None,
                'duration': None,
                'remaining_count': None,
                'total_count': None,
                'allow_reverify': 0,
                'message': '未验证'
            }
            print("✓ 卡密信息存储初始化完成")

            # 设置窗口基本属性
            self.setWindowTitle('健康证管理系统 - MED')
            self.setWindowIcon(QIcon('icons/tb.ico'))
            self.resize(760, 1032)  # 用户指定的可调节窗口尺寸
            self.setMinimumSize(760, 1032)  # 设置最小尺寸限制

            print("✓ 窗口属性设置完成")

            # 加载认证信息
            self.load_auth_from_login()

            # 初始化认证管理器
            self.auth_manager = AuthManager()

            # 初始化性能监控器
            from performance_monitor import PerformanceMonitor
            self.performance_monitor = PerformanceMonitor(self)
            self.performance_monitor.memory_warning.connect(self.on_memory_warning)
            self.performance_monitor.cpu_warning.connect(self.on_cpu_warning)
            self.performance_monitor.performance_report.connect(self.on_performance_report)
            self.performance_monitor.start_monitoring()

            # 配置热重载定时器
            self.config_reload_timer = QTimer()
            self.config_reload_timer.timeout.connect(self.reload_configurations)
            self.config_reload_timer.start(60000)  # 每分钟检查配置变化

            # 应用预认证信息
            if hasattr(self, 'login_auth_data'):
                self.apply_login_auth()

            # 计算自适应尺寸
            self.window_width = 760
            self.window_height = 1032
            self.scale_factor = min(self.window_width / 800, self.window_height / 1000)
            
            print("✓ 缩放因子计算完成")
            
            # 初始化UI组件
            self.init_ui_components()
            
            print("✓ UI组件初始化完成")
            
            # 设置定时器
            self.setup_timers()
            
            print("✓ 定时器设置完成")
            
            # 更新认证状态
            self.update_auth_status()
            
            print("✓ 健康证管理系统初始化完成")

            # 延迟更新卡密状态显示，确保UI组件已创建
            QTimer.singleShot(100, self.update_card_status)

            # 注册关闭事件处理
            import atexit
            atexit.register(self.cleanup_on_exit)

        except Exception as e:
            print(f"✗ 健康证管理系统初始化失败: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def load_auth_from_login(self):
        """从登录系统加载认证信息"""
        try:
            temp_auth_file = "temp_auth.json"
            current_dir = os.getcwd()
            full_path = os.path.join(current_dir, temp_auth_file)

            print(f"🔍 检查认证文件:")
            print(f"  当前工作目录: {current_dir}")
            print(f"  认证文件路径: {full_path}")
            print(f"  文件是否存在: {os.path.exists(temp_auth_file)}")

            if os.path.exists(temp_auth_file):
                print(f"📖 开始读取认证文件...")
                with open(temp_auth_file, 'r', encoding='utf-8') as f:
                    self.login_auth_data = json.load(f)

                print(f"✓ 认证文件读取成功，数据大小: {len(str(self.login_auth_data))} 字符")

                # 解析卡密信息
                self.parse_card_info()

                print(f"✓ 已加载登录认证信息")
                print(f"  登录时间: {self.login_auth_data.get('login_time', 'N/A')}")
                print(f"  卡密状态: {self.login_auth_data.get('card_auth', {}).get('message', 'N/A')}")
                print(f"  卡密类型: {self.card_info.get('card_type', 'N/A')}")
            else:
                print("⚠ 未找到登录认证信息，使用默认配置")
                self.login_auth_data = None
        except Exception as e:
            print(f"✗ 加载登录认证信息失败: {e}")
            import traceback
            traceback.print_exc()
            self.login_auth_data = None

    def parse_card_info(self):
        """解析卡密信息"""
        try:
            print("🔍 开始解析卡密信息...")

            if not self.login_auth_data:
                print("⚠️ 没有登录认证数据，无法解析卡密信息")
                return

            card_auth = self.login_auth_data.get('card_auth', {})
            card_data = card_auth.get('data', {})

            print(f"📊 登录认证数据: {self.login_auth_data}")
            print(f"🔐 卡密认证数据: {card_auth}")
            print(f"📋 卡密详细数据: {card_data}")

            if card_data:
                # 更新卡密信息
                self.card_info.update({
                    'card_key': card_data.get('card_key', ''),
                    'card_type': card_data.get('card_type', 'unknown'),
                    'status': card_data.get('status', 0),
                    'use_time': card_data.get('use_time', ''),
                    'expire_time': card_data.get('expire_time', ''),
                    'duration': card_data.get('duration', 0),
                    'remaining_count': card_data.get('remaining_count', 0),
                    'total_count': card_data.get('total_count', 0),
                    'allow_reverify': card_data.get('allow_reverify', 0),
                    'message': card_auth.get('message', '验证成功')
                })

                print(f"✓ 卡密信息解析完成:")
                print(f"  类型: {self.card_info['card_type']}")
                if self.card_info['card_type'] == 'time':
                    print(f"  到期时间: {self.card_info['expire_time']}")
                    print(f"  有效期: {self.card_info['duration']}天")
                elif self.card_info['card_type'] == 'count':
                    print(f"  剩余次数: {self.card_info['remaining_count']}/{self.card_info['total_count']}")
            else:
                print("⚠ 未找到有效的卡密数据")

        except Exception as e:
            print(f"✗ 解析卡密信息失败: {e}")
            # 保持默认值
    
    def apply_login_auth(self):
        """应用登录系统的认证信息"""
        try:
            if self.login_auth_data and 'health_auth' in self.login_auth_data:
                health_auth = self.login_auth_data['health_auth']
                
                # 设置健康证系统的认证信息
                if 'access_token' in health_auth:
                    self.auth_manager.access_token = health_auth['access_token']
                    self.auth_manager.refresh_token = health_auth.get('refresh_token')
                    self.auth_manager.expires_at = health_auth.get('expires_at', 0)
                    
                    print("✓ 健康证系统认证信息已应用")
                    print(f"  Token: {self.auth_manager.access_token[:20]}...")
                    
        except Exception as e:
            print(f"✗ 应用登录认证信息失败: {e}")
    
    def init_ui_components(self):
        """初始化UI组件"""
        # 这里包含原来的UI初始化代码
        # 为了简洁，只显示关键部分
        
        # 初始化自动刷新相关变量
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.auto_refresh_page)
        self.auto_refresh_interval = 30000
        self.is_auto_refresh_enabled = False
        
        # URL更新控制
        self.last_url_update = 0
        self.url_update_interval = 500

        # 安全设置：强制隐藏敏感URL信息（不要修改此设置）
        self.hide_sensitive_urls = True
        
        # 创建中央部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(0)

        # 创建QSplitter用于可调整的左右面板
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.setStyleSheet("""
            QSplitter::handle {
                background: #e9ecef;
                width: 3px;
                border-radius: 1px;
            }
            QSplitter::handle:hover {
                background: #007bff;
            }
        """)

        # 创建左侧面板和右侧浏览器区域
        left_panel = self.create_left_panel_widget()
        right_panel = self.create_browser_area_widget()

        # 添加到分割器
        self.splitter.addWidget(left_panel)
        self.splitter.addWidget(right_panel)

        # 设置初始比例 (为浏览器预留445px)
        total_width = self.window_width - 10  # 减去边距
        browser_width = 465  # 445px浏览器 + 20px边距
        left_width = total_width - browser_width
        self.splitter.setSizes([left_width, browser_width])

        # 设置最小尺寸
        self.splitter.setChildrenCollapsible(False)  # 防止面板被完全折叠

        # 将分割器添加到主布局
        main_layout.addWidget(self.splitter)
        
        print("✓ UI布局创建完成")
    
    def setup_timers(self):
        """设置定时器 - 性能优化版"""
        # 获取性能配置
        performance_config = self.config_manager.get_config('performance_config')

        # 内存清理定时器 - 使用配置的间隔
        cleanup_interval = performance_config.get('memory_cleanup_interval', 300000)
        self.memory_timer = QTimer()
        self.memory_timer.timeout.connect(self.cleanup_memory_enhanced)
        self.memory_timer.start(cleanup_interval)

        # 自动刷新定时器（默认关闭）
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.auto_refresh_page)
        self.is_auto_refresh_enabled = False

        # 性能监控定时器
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self.monitor_system_performance)
        self.performance_timer.start(60000)  # 每分钟检查一次

        # 卡密状态更新定时器
        self.card_status_timer = QTimer()
        self.card_status_timer.timeout.connect(self.update_card_status)
        self.card_status_timer.start(30000)  # 每30秒更新一次卡密状态

        print(f"✓ 定时器设置完成 - 内存清理间隔: {cleanup_interval//1000}秒")

    def handle_photo_click(self, event):
        """处理照片上传区域点击事件"""
        try:
            # 只响应左键点击
            if event.button() == Qt.LeftButton:
                self.select_photo()
        except Exception as e:
            print(f"处理照片点击事件失败: {e}")
            QMessageBox.warning(self, "错误", "照片选择功能暂时不可用，请稍后重试")

    # 选择照片
    def select_photo(self):
        """选择照片文件 - 增强异常处理版本"""
        try:
            options = QFileDialog.Options()
            # 添加更多安全选项
            options |= QFileDialog.DontUseNativeDialog

            file_name, _ = QFileDialog.getOpenFileName(
                self,
                "选择照片",
                "",
                "图片文件 (*.jpg *.jpeg *.png *.gif);;所有文件 (*)",
                options=options
            )

            if file_name:
                # 验证文件是否存在
                if not os.path.exists(file_name):
                    QMessageBox.warning(self, "文件错误", "所选文件不存在")
                    return

                # 检查文件大小（最大2MB）
                try:
                    file_size = os.path.getsize(file_name) / (1024 * 1024)  # 转换为MB
                except OSError as e:
                    QMessageBox.warning(self, "文件错误", f"无法读取文件大小: {e}")
                    return

                if file_size > 2:
                    QMessageBox.warning(self, "文件过大", "所选文件超过2MB，请选择更小的文件。")
                    return

                self.photo_path = file_name
                file_info = os.path.basename(file_name)
                file_size_text = f" ({file_size:.1f}MB)" if file_size > 0.1 else ""

                # 更新照片信息显示
                if hasattr(self, 'photo_info'):
                    self.photo_info.setText(f"✅ {file_info}{file_size_text}")
                    self.photo_info.setStyleSheet("""
                        QLabel {
                            font-size: 12px;
                            color: #28a745;
                            background: transparent;
                            border: none;
                            padding: 5px 0;
                        }
                    """)

                print(f"✓ 照片选择成功: {file_info} ({file_size:.1f}MB)")

        except Exception as e:
            print(f"选择照片失败: {e}")
            QMessageBox.critical(self, "选择照片失败", f"照片选择过程中发生错误：{str(e)}")
            # 重置照片路径
            self.photo_path = None
            if hasattr(self, 'photo_info'):
                self.photo_info.setText("未选择文件")

    # 生成健康证
    def generate_health_cert(self):
        # 检查认证状态
        if not self.auth_manager.is_token_valid():
            QMessageBox.warning(self, "认证失败", "请先进行API认证")
            return
        
        # 获取表单数据
        name = self.name_edit.text().strip()
        gender = self.gender_combo.currentText()
        id_number = self.id_edit.text().strip()
        org_name = self.unit_edit.text().strip()
        issue_date = self.date_edit.date().toString("yyyy-MM-dd")
        valid_date = self.expiry_edit.date().toString("yyyy-MM-dd")
        
        # 详细验证
        if not name:
            QMessageBox.warning(self, "信息不完整", "请填写姓名")
            return
        
        if not id_number:
            QMessageBox.warning(self, "信息不完整", "请填写身份证号")
            return
        
        # 验证身份证号格式
        if not self.validate_id_number(id_number):
            QMessageBox.warning(self, "格式错误", "请输入正确的18位身份证号")
            return
        
        if not org_name:
            QMessageBox.warning(self, "信息不完整", "请填写体检单位")
            return
        
        # 打印调试信息
        print(f"=== 创建健康证调试信息 ===")
        print(f"姓名: {name}")
        print(f"性别: {gender}")
        print(f"身份证号: {id_number}")
        print(f"体检单位: {org_name}")
        print(f"体检日期: {issue_date}")
        print(f"有效期至: {valid_date}")
        print(f"认证状态: {self.auth_manager.is_token_valid()}")
        print(f"访问令牌: {self.auth_manager.access_token[:20] if self.auth_manager.access_token else 'None'}...")
        
        # 调用API创建健康证
        self.create_health_cert_api(name, gender, id_number, org_name, issue_date, valid_date)

    def validate_id_number(self, id_number):
        """验证身份证号格式"""
        import re
        # 18位身份证号正则表达式
        pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
        return re.match(pattern, id_number) is not None

    # 保存健康证
    def save_health_cert(self):
        if not self.tabs.currentWidget():
            QMessageBox.warning(self, "错误", "没有可保存的健康证")
            return
            
        # 获取姓名作为默认文件名
        name = self.name_edit.text() or "健康证"
        default_filename = f"{name}_{datetime.now().strftime('%Y%m%d')}"
        
        # 打开文件保存对话框
        options = QFileDialog.Options()
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存健康证",
            default_filename,
            "PNG图片 (*.png);;JPEG图片 (*.jpg);;PDF文档 (*.pdf)",
            options=options
        )
        
        if not file_path:
            return
            
        # 根据文件扩展名选择保存方式
        if file_path.endswith('.pdf'):
            self.save_as_pdf(file_path)
        else:
            self.save_as_image(file_path)
    
    # 保存为PDF
    def save_as_pdf(self, file_path):
        """保存为PDF"""
        try:
            current_browser = self.tabs.currentWidget()
            if current_browser:
                # 创建打印机对象
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                
                # 打印页面
                current_browser.page().print(printer, lambda success: 
                    QMessageBox.information(self, "保存成功", f"健康证已保存为: {file_path}") if success 
                    else QMessageBox.warning(self, "保存失败", "PDF保存失败"))
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存PDF时发生错误: {str(e)}")

    # 保存为图片
    def save_as_image(self, file_path):
        """保存为图片"""
        try:
            current_browser = self.tabs.currentWidget()
            if current_browser:
                # 获取页面截图
                current_browser.grab().save(file_path)
                QMessageBox.information(self, "保存成功", f"健康证已保存为: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存图片时发生错误: {str(e)}")
        
    # 响应回车按钮，将浏览器当前访问的URL设置为用户输入的URL
    def navigate_to_url(self):
        current_url = QUrl(self.urlbar.text())
        if current_url.scheme() == '':
            current_url.setScheme('http')
        self.tabs.currentWidget().load(current_url)

    # 将当前网页的链接更新到地址栏
    def renew_urlbar(self, url, browser=None):
        # 非当前窗口不更新URL
        if browser != self.tabs.currentWidget():
            return
        
        # 限制更新频率
        current_time = time.time() * 1000
        if current_time - self.last_url_update < self.url_update_interval:
            return
        self.last_url_update = current_time
        
        url_string = url.toString()
        
        # 根据配置决定是否隐藏敏感URL
        if self.hide_sensitive_urls:
            # 检查是否包含健康证相关的敏感信息
            if "cert.php" in url_string:
                # 隐藏所有健康证页面的参数信息
                self.urlbar.setText("健康证查看页面")
            elif "**************" in url_string:
                # 隐藏内网IP，只显示功能描述
                if "api" in url_string:
                    self.urlbar.setText("API接口")
                elif "zm.html" in url_string:
                    self.urlbar.setText("系统首页")
                elif "mobile.php" in url_string:
                    self.urlbar.setText("手机版")
                else:
                    self.urlbar.setText("内部系统")
            elif self.contains_sensitive_params(url_string):
                # 检查是否包含其他敏感参数
                self.urlbar.setText("系统页面")
            elif url_string == 'about:blank':
                self.urlbar.setText("准备就绪")
            else:
                # 对于其他URL，检查是否包含敏感信息
                cleaned_url = self.clean_sensitive_url(url_string)
                self.urlbar.setText(cleaned_url)
        else:
            self.urlbar.setText(url_string)
        
        self.urlbar.setCursorPosition(0)

    def contains_sensitive_params(self, url_string):
        """检查URL是否包含敏感参数"""
        import re

        # 定义敏感参数模式
        sensitive_patterns = [
            r'[?&]id=\w+',           # id参数
            r'[?&]cert_\w+',         # cert_开头的参数
            r'[?&]name=\w+',         # name参数
            r'[?&]idcard=\w+',       # idcard参数
            r'[?&]phone=\w+',        # phone参数
            r'[?&]user_id=\w+',      # user_id参数
            r'[?&]token=\w+',        # token参数
            r'\d{15,18}',            # 身份证号模式
            r'1[3-9]\d{9}',          # 手机号模式
        ]

        for pattern in sensitive_patterns:
            if re.search(pattern, url_string):
                return True
        return False

    def clean_sensitive_url(self, url_string):
        """清理URL中的敏感信息"""
        import re

        # 如果包含敏感信息，返回通用描述
        if self.contains_sensitive_params(url_string):
            return "系统页面"

        # 移除常见的敏感参数
        cleaned_url = re.sub(r'[?&]id=\w+', '', url_string)
        cleaned_url = re.sub(r'[?&]cert_\w+', '', cleaned_url)
        cleaned_url = re.sub(r'[?&]name=\w+', '', cleaned_url)
        cleaned_url = re.sub(r'[?&]idcard=\w+', '', cleaned_url)
        cleaned_url = re.sub(r'[?&]phone=\w+', '', cleaned_url)
        cleaned_url = re.sub(r'[?&]user_id=\w+', '', cleaned_url)
        cleaned_url = re.sub(r'[?&]token=\w+', '', cleaned_url)

        # 清理多余的?和&符号
        cleaned_url = re.sub(r'[?&]+$', '', cleaned_url)
        cleaned_url = re.sub(r'[?]&', '?', cleaned_url)
        cleaned_url = re.sub(r'&+', '&', cleaned_url)

        return cleaned_url

    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        self.is_auto_refresh_enabled = not self.is_auto_refresh_enabled
        
        if self.is_auto_refresh_enabled:
            self.auto_refresh_timer.start(self.auto_refresh_interval)
            self.auto_refresh_btn.setToolTip(f"自动刷新已开启 (每{self.auto_refresh_interval//1000}秒)")
            self.show_temporary_message(f"自动刷新已开启，每{self.auto_refresh_interval//1000}秒刷新一次")
        else:
            self.auto_refresh_timer.stop()
            self.auto_refresh_btn.setToolTip("自动刷新已关闭 - 点击开启")
            self.show_temporary_message("自动刷新已关闭")
    
    def auto_refresh_page(self):
        """自动刷新当前页面"""
        if self.is_auto_refresh_enabled:
            current_browser = self.tabs.currentWidget()
            if current_browser:
                current_browser.reload()

    def initialize_auth(self):
        """初始化认证管理器"""
        self.auth_manager = AuthManager()
        
        # 检查API兼容性
        api_compatible, api_message = self.auth_manager.check_api_compatibility()
        if not api_compatible:
            QMessageBox.warning(self, "API连接警告", f"无法连接到API服务器: {api_message}\n请检查网络连接和服务器配置。")
            print(f"API兼容性检查失败: {api_message}")
        else:
            print(f"API兼容性检查成功: {api_message}")
        
        # 执行认证流程
        self.perform_auth()
    
    def perform_auth(self):
        """执行认证操作"""
        print("=== 开始认证流程 ===")
        
        # 先测试基础连接
        try:
            test_url = "http://localhost/"
            test_response = requests.get(test_url, timeout=5)
            print(f"基础连接测试: {test_response.status_code}")
        except Exception as e:
            print(f"基础连接测试失败: {e}")
            self.auth_btn.setText("初始化认证")
            self.auth_btn.setEnabled(True)
            self.auth_status.setText(f"✗ 网络连接失败: {str(e)}")
            self.auth_status.setStyleSheet("color: #e74c3c; font-size: 9pt;")
            return
        
        success, message = self.auth_manager.login_api()
        
        self.auth_btn.setText("初始化认证")
        self.auth_btn.setEnabled(True)
        
        if success:
            self.auth_status.setText(f"✓ {message}")
            self.auth_status.setStyleSheet("color: #27ae60; font-size: 9pt;")
            print(f"认证成功: {message}")
        else:
            self.auth_status.setText(f"✗ {message}")
            self.auth_status.setStyleSheet("color: #e74c3c; font-size: 9pt;")
            print(f"认证失败: {message}")
        
        self.update_auth_status()
    
    def update_auth_status(self):
        """更新认证状态显示"""
        status_font_size = self.get_scaled_font_size(12)
        status_padding = self.get_scaled_size(8)
        border_radius = self.get_scaled_size(6)
        
        if self.auth_manager.is_token_valid():
            remaining_time = int(self.auth_manager.expires_at - time.time())
            hours = remaining_time // 3600
            minutes = (remaining_time % 3600) // 60
            time_str = f"{hours}小时{minutes}分钟" if hours > 0 else f"{minutes}分钟"
            self.auth_status.setText(f"✅ 认证有效 (剩余: {time_str})")
            self.auth_status.setStyleSheet(f"""
                QLabel {{
                    color: #27ae60;
                    font-size: {status_font_size}px;
                    background: #d5f4e6;
                    padding: {status_padding}px;
                    border-radius: {border_radius}px;
                    border-left: 3px solid #27ae60;
                }}
            """)
        else:
            self.auth_status.setText("⚠️ 未认证或已过期")
            self.auth_status.setStyleSheet(f"""
                QLabel {{
                    color: #e74c3c;
                    font-size: {status_font_size}px;
                    background: #fdf2f2;
                    padding: {status_padding}px;
                    border-radius: {border_radius}px;
                    border-left: 3px solid #e74c3c;
                }}
            """)

    # 添加新的标签页
    def add_new_tab(self, qurl=QUrl('about:blank'), label='新标签页'):
        # 设置浏览器
        self.browser = WebView(self)

        # 设置浏览器固定尺寸为445*979
        self.browser.setFixedSize(445, 979)

        # 设置页面优化
        page = self.browser.page()
        page.setBackgroundColor(QColor(255, 255, 255))  # 设置白色背景
        
        # 禁用不必要的页面功能
        try:
            page.settings().setAttribute(QWebEngineSettings.ErrorPageEnabled, False)
            page.settings().setAttribute(QWebEngineSettings.FullScreenSupportEnabled, False)
        except:
            pass
        
        # 如果是空白页面，不立即加载
        if qurl.toString() != 'about:blank':
            self.browser.load(qurl)
        
        # 添加标签页
        tab_index = self.tabs.addTab(self.browser, label)
        self.tabs.setCurrentIndex(tab_index)
        
        # 绑定URL变化事件
        self.browser.urlChanged.connect(lambda url: self.renew_urlbar(url, self.browser))
        
        # 绑定加载完成事件
        self.browser.loadFinished.connect(
            lambda success: self.on_load_finished(success, tab_index, self.browser)
        )
        
        return self.browser
    
    def on_load_finished(self, success, tab_index, browser):
        """页面加载完成回调"""
        if not success:
            return
            
        if browser != self.tabs.currentWidget():
            return
            
        # 简化标题更新逻辑
        url = browser.url().toString()
        if 'zm.html' in url:
            self.tabs.setTabText(tab_index, '系统首页')
        elif 'mobile.php' in url:
            self.tabs.setTabText(tab_index, '手机版')
        elif 'cert.php' in url:
            self.tabs.setTabText(tab_index, '健康证查看')
        elif 'welcome.html' in url or url == 'about:blank':
            self.tabs.setTabText(tab_index, '欢迎页面')
        else:
            title = browser.page().title()
            if title and len(title) > 0:
                # 限制标题长度
                display_title = title[:15] + '...' if len(title) > 15 else title
                self.tabs.setTabText(tab_index, display_title)
            else:
                self.tabs.setTabText(tab_index, '页面')

    def cleanup_memory_enhanced(self):
        """增强的定期内存清理 - 性能优化版"""
        try:
            print("执行增强内存清理...")

            # 清理所有标签页
            for i in range(self.tabs.count()):
                browser = self.tabs.widget(i)
                if browser and hasattr(browser, 'cleanup_memory'):
                    browser.cleanup_memory()

            # 系统级内存清理
            import gc
            gc.collect()

            # 检查内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                print(f"✓ 内存清理完成，当前使用: {memory_mb:.1f}MB")
            except:
                print("✓ 内存清理完成")

        except Exception as e:
            print(f"增强内存清理失败: {e}")

    def monitor_system_performance(self):
        """监控系统性能"""
        try:
            import psutil

            # 检查内存使用
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            cpu_percent = process.cpu_percent()

            performance_config = self.config_manager.get_config('performance_config')
            memory_limit = performance_config.get('memory_limit', 512)

            # 如果内存使用过高，执行清理
            if memory_mb > memory_limit:
                print(f"⚠ 内存使用过高: {memory_mb:.1f}MB，执行清理...")
                self.cleanup_memory_enhanced()

            # 如果CPU使用过高，暂停一些非关键功能
            if cpu_percent > 80:
                print(f"⚠ CPU使用过高: {cpu_percent:.1f}%")
                # 暂停自动刷新
                if self.is_auto_refresh_enabled:
                    self.auto_refresh_timer.stop()
                    QTimer.singleShot(30000, lambda: self.auto_refresh_timer.start() if self.is_auto_refresh_enabled else None)

        except Exception as e:
            print(f"性能监控失败: {e}")

    def reload_configurations(self):
        """重新加载配置文件"""
        try:
            # 重新加载配置管理器（如果可用）
            if hasattr(self, 'config_manager') and self.config_manager:
                self.config_manager.reload_all_configs()

                # 更新认证管理器配置
                main_config = self.config_manager.get_config('main_config')
                if hasattr(self.auth_manager, 'config_manager') and self.auth_manager.config_manager:
                    self.auth_manager.auto_refresh_threshold = main_config.get('auto_refresh_threshold', 300)
                    self.auth_manager.network_timeout = main_config.get('network_timeout', 30)
                    self.auth_manager.max_retries = main_config.get('max_retries', 3)

                # 更新性能监控配置
                performance_config = self.config_manager.get_config('performance_config')
                if hasattr(self, 'performance_monitor'):
                    self.performance_monitor.memory_limit = performance_config.get('memory_limit', 512)

                print("✓ 配置热重载完成")
            else:
                print("⚠ 配置管理器不可用，跳过热重载")

        except Exception as e:
            print(f"✗ 配置热重载失败: {e}")

    def on_memory_warning(self, memory_mb):
        """内存警告处理"""
        print(f"⚠ 内存使用警告: {memory_mb:.1f}MB")
        # 执行内存清理
        self.cleanup_memory_enhanced()

        # 如果内存仍然过高，显示用户提示
        memory_limit = 512  # 默认值
        if hasattr(self, 'config_manager') and self.config_manager:
            memory_limit = self.config_manager.get_config('performance_config', 'memory_limit', 512)

        if memory_mb > memory_limit * 1.2:
            self.show_temporary_message(f"内存使用过高: {memory_mb:.1f}MB，已执行清理")

    def on_cpu_warning(self, cpu_percent):
        """CPU警告处理"""
        print(f"⚠ CPU使用警告: {cpu_percent:.1f}%")

        # 暂时停止非关键功能
        if self.is_auto_refresh_enabled:
            self.auto_refresh_timer.stop()
            print("已暂停自动刷新以降低CPU使用")

            # 30秒后恢复
            QTimer.singleShot(30000, lambda: self.auto_refresh_timer.start() if self.is_auto_refresh_enabled else None)

    def on_performance_report(self, report):
        """性能报告处理"""
        try:
            memory_avg = report['memory_stats']['average_mb']
            cpu_avg = report['cpu_stats']['average_percent']

            print(f"📊 性能报告: 平均内存={memory_avg:.1f}MB, 平均CPU={cpu_avg:.1f}%")

            # 如果性能持续不佳，建议用户操作
            if memory_avg > 400 or cpu_avg > 60:
                self.show_temporary_message("系统资源使用较高，建议关闭不必要的标签页")

        except Exception as e:
            print(f"处理性能报告失败: {e}")

    def update_card_status(self):
        """更新卡密状态显示"""
        try:
            print("🔄 开始更新卡密状态显示...")

            if not hasattr(self, 'card_type_label'):
                print("⚠️ UI组件还未初始化完成，跳过卡密状态更新")
                return

            # 调试：打印当前卡密信息
            print(f"📋 当前卡密信息: {self.card_info}")

            card_type = self.card_info.get('card_type', 'unknown')
            status = self.card_info.get('status', 0)

            print(f"🎯 卡密类型: {card_type}, 状态: {status}")

            if status == 1:  # 验证成功
                if card_type == 'time':
                    self.update_time_card_display()
                elif card_type == 'count':
                    self.update_count_card_display()
                else:
                    self.update_unknown_card_display()
            else:
                self.update_unverified_card_display()

        except Exception as e:
            print(f"更新卡密状态失败: {e}")

    def update_time_card_display(self):
        """更新时间卡密显示"""
        try:
            expire_time = self.card_info.get('expire_time', '')
            duration = self.card_info.get('duration', 0)
            use_time = self.card_info.get('use_time', '')

            # 更新类型标签
            self.card_type_label.setText("🕒 时间卡密")

            # 计算剩余时间
            if expire_time:
                try:
                    from datetime import datetime
                    expire_dt = datetime.strptime(expire_time, '%Y-%m-%d %H:%M:%S')
                    current_dt = datetime.now()
                    remaining = expire_dt - current_dt

                    if remaining.total_seconds() > 0:
                        days = remaining.days
                        hours = remaining.seconds // 3600
                        total_hours = remaining.total_seconds() / 3600

                        # 到期提醒逻辑
                        if total_hours <= 24:  # 24小时内到期
                            minutes = (remaining.seconds % 3600) // 60
                            status_text = f"剩余: {hours}小时{minutes}分钟"
                            self.card_status_label.setText(f"🚨 {status_text}")
                            self.show_expiry_warning(f"卡密将在{hours}小时{minutes}分钟后到期")
                        elif days <= 3:  # 3天内到期
                            status_text = f"剩余: {days}天{hours}小时"
                            self.card_status_label.setText(f"⚠️ {status_text}")
                        else:
                            status_text = f"剩余: {days}天{hours}小时"
                            self.card_status_label.setText(f"✅ {status_text}")
                    else:
                        self.card_status_label.setText("❌ 已过期")
                        self.show_expiry_warning("卡密已过期，请联系管理员")

                except Exception as e:
                    self.card_status_label.setText(f"到期: {expire_time}")
            else:
                self.card_status_label.setText("状态: 已激活")

            # 详细信息 - 紧凑格式
            detail_text = f"{duration}天"
            if use_time:
                detail_text += f" | {use_time.split()[0]}"  # 只显示日期部分
            self.card_detail_label.setText(detail_text)

        except Exception as e:
            print(f"更新时间卡密显示失败: {e}")

    def update_count_card_display(self):
        """更新次数卡密显示"""
        try:
            remaining_count_raw = self.card_info.get('remaining_count', 0)
            total_count_raw = self.card_info.get('total_count', 0)
            use_time = self.card_info.get('use_time', '')

            # 转换为整数，处理字符串类型
            try:
                remaining_count = int(remaining_count_raw) if remaining_count_raw else 0
                total_count = int(total_count_raw) if total_count_raw else 0
            except (ValueError, TypeError):
                remaining_count = 0
                total_count = 0
                print(f"⚠️ 卡密次数转换失败: remaining={remaining_count_raw}, total={total_count_raw}")

            # 更新类型标签
            self.card_type_label.setText("🎯 次数卡密")

            # 更新状态标签
            if remaining_count > 0:
                if total_count > 0 and remaining_count > total_count * 0.3:  # 剩余超过30%
                    self.card_status_label.setText(f"✅ 剩余: {remaining_count}次")
                elif remaining_count > 5:  # 剩余超过5次
                    self.card_status_label.setText(f"⚠️ 剩余: {remaining_count}次")
                else:  # 剩余较少
                    self.card_status_label.setText(f"🔴 剩余: {remaining_count}次")
            else:
                self.card_status_label.setText("❌ 次数已用完")

            # 详细信息 - 紧凑格式
            detail_text = f"总{total_count}次"
            if use_time:
                detail_text += f" | {use_time.split()[0]}"  # 只显示日期部分
            self.card_detail_label.setText(detail_text)

        except Exception as e:
            print(f"更新次数卡密显示失败: {e}")

    def update_unknown_card_display(self):
        """更新未知类型卡密显示"""
        self.card_type_label.setText("❓ 未知类型")
        self.card_status_label.setText("已验证")
        message = self.card_info.get('message', '验证成功')
        self.card_detail_label.setText(message[:10] + "..." if len(message) > 10 else message)

    def update_unverified_card_display(self):
        """更新未验证卡密显示"""
        self.card_type_label.setText("🔒 未验证")
        self.card_status_label.setText("等待验证")
        self.card_detail_label.setText("请先验证")

    def show_expiry_warning(self, message):
        """显示到期警告"""
        try:
            # 避免重复显示相同警告
            if hasattr(self, '_last_warning') and self._last_warning == message:
                return

            self._last_warning = message

            # 在状态栏显示警告（如果有的话）
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(f"⚠️ {message}", 10000)  # 显示10秒

            # 在控制台输出警告
            print(f"⚠️ 卡密到期提醒: {message}")

            # 可选：显示系统通知（需要额外的库支持）
            # self.show_system_notification("卡密到期提醒", message)

        except Exception as e:
            print(f"显示到期警告失败: {e}")

    def show_system_notification(self, title, message):
        """显示系统通知（可选功能）"""
        try:
            # 这里可以集成系统通知功能
            # 例如使用 plyer 库或 Windows Toast 通知
            pass
        except Exception as e:
            print(f"显示系统通知失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            print("正在关闭健康证管理系统...")

            # 停止所有定时器
            if hasattr(self, 'memory_timer'):
                self.memory_timer.stop()
            if hasattr(self, 'auto_refresh_timer'):
                self.auto_refresh_timer.stop()
            if hasattr(self, 'performance_timer'):
                self.performance_timer.stop()
            if hasattr(self, 'config_reload_timer'):
                self.config_reload_timer.stop()
            if hasattr(self, 'card_status_timer'):
                self.card_status_timer.stop()

            # 停止性能监控
            if hasattr(self, 'performance_monitor'):
                self.performance_monitor.stop_monitoring()

            # 停止认证管理器的定时器
            if hasattr(self, 'auth_manager') and hasattr(self.auth_manager, 'refresh_timer'):
                self.auth_manager.refresh_timer.stop()

            # 清理所有WebView
            for i in range(self.tabs.count()):
                browser = self.tabs.widget(i)
                if browser and hasattr(browser, 'cleanup_memory'):
                    browser.cleanup_memory()

            # 执行最终清理
            self.cleanup_on_exit()

            print("✓ 健康证管理系统已安全关闭")
            event.accept()

        except Exception as e:
            print(f"关闭过程中发生错误: {e}")
            event.accept()  # 即使出错也要关闭

    def cleanup_on_exit(self):
        """退出时的清理工作"""
        try:
            print("执行退出清理...")

            # 清理临时文件
            temp_files = ["temp_auth.json", "temp_auth.backup.json"]
            for temp_file in temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        print(f"✓ 已清理临时文件: {temp_file}")
                except:
                    pass

            # 保存最终配置
            if hasattr(self, 'config_manager') and self.config_manager:
                try:
                    # 保存当前窗口状态
                    window_config = {
                        'window_width': self.width(),
                        'window_height': self.height(),
                        'last_closed': datetime.now().isoformat()
                    }

                    for key, value in window_config.items():
                        self.config_manager.set_config('main_config', key, value)

                except Exception as e:
                    print(f"保存窗口配置失败: {e}")

            # 强制垃圾回收
            import gc
            gc.collect()

            print("✓ 退出清理完成")

        except Exception as e:
            print(f"退出清理失败: {e}")

    # 双击标签栏打开新页面
    def tab_open(self, i):
        if i == -1:
            self.add_new_tab()
            
    def current_tab_changed(self, i):
        # 更新地址栏显示当前标签页的URL
        current_browser = self.tabs.currentWidget()
        if current_browser:
            self.renew_urlbar(current_browser.url(), current_browser)
        
    def close_current_tab(self, i):
        # 若当前标签页只有一个则不关闭
        if self.tabs.count() < 2:
            return
        self.tabs.removeTab(i)

    def upload_photo(self):
        """上传照片到服务器"""
        if not self.photo_path:
            return None
        
        try:
            # 检查文件是否存在
            if not os.path.exists(self.photo_path):
                QMessageBox.warning(self, "文件错误", "选择的文件不存在")
                return None
            
            # 检查文件大小
            file_size = os.path.getsize(self.photo_path) / (1024 * 1024)  # MB
            if file_size > 2:
                QMessageBox.warning(self, "文件过大", f"文件大小 {file_size:.1f}MB 超过2MB限制")
                return None
            
            # 检查文件格式 - 与服务器要求保持一致
            file_ext = os.path.splitext(self.photo_path)[1].lower()
            allowed_formats = ['.jpg', '.png', '.gif']  # 移除.jpeg，只保留服务器支持的格式
            if file_ext not in allowed_formats:
                QMessageBox.warning(self, "格式错误", f"不支持的文件格式: {file_ext}\n服务器只支持JPG、PNG或GIF格式")
                return None
            
            # 获取认证头
            auth_header = self.auth_manager.get_auth_header()
            if not auth_header:
                QMessageBox.warning(self, "认证失败", "无有效的访问令牌")
                return None
            
            # 打印调试信息
            print(f"上传文件: {self.photo_path}")
            print(f"文件大小: {file_size:.2f}MB")
            print(f"文件扩展名: {file_ext}")
            
            # 首先尝试简化版API
            return self.try_upload_simple() or self.try_upload_original()
            
        except Exception as e:
            QMessageBox.critical(self, "上传错误", f"照片上传失败：{str(e)}")
            return None

    def try_upload_simple(self):
        """尝试简化版上传API"""
        try:
            url = self.auth_manager.upload_photo_url  # 使用RESTful风格API
            auth_header = self.auth_manager.get_auth_header()
            
            with open(self.photo_path, 'rb') as f:
                original_filename = os.path.basename(self.photo_path)
                file_ext = os.path.splitext(self.photo_path)[1].lower()
                
                files = {'photo': (original_filename, f, 'image/' + file_ext[1:])}
                response = requests.post(url, files=files, headers=auth_header, timeout=30)
                
                print(f"简化版API响应状态码: {response.status_code}")
                print(f"简化版API响应内容: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200:
                        filename = result.get("data", {}).get("filename")
                        if filename:
                            print(f"简化版上传成功，文件名: {filename}")
                            return filename
                
                return None
                
        except Exception as e:
            print(f"简化版上传失败: {str(e)}")
            return None

    def try_upload_original(self):
        """尝试原始上传API"""
        try:
            print("尝试备用上传方式...")
            url = self.auth_manager.upload_url  # 使用RESTful风格API
            auth_header = self.auth_manager.get_auth_header()
            
            with open(self.photo_path, 'rb') as f:
                original_filename = os.path.basename(self.photo_path)
                file_ext = os.path.splitext(self.photo_path)[1].lower()
                
                files = {'photo': (original_filename, f, 'image/' + file_ext[1:])}
                response = requests.post(url, files=files, headers=auth_header, timeout=30)
                
                print(f"原始API响应状态码: {response.status_code}")
                print(f"原始API响应内容: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200:
                        filename = result.get("data", {}).get("filename")
                        if filename:
                            print(f"原始API上传成功，文件名: {filename}")
                            return filename
                
                # 显示具体错误信息
                try:
                    result = response.json()
                    error_msg = result.get("message", "上传失败")
                    QMessageBox.warning(self, "上传失败", f"所有上传方式都失败: {error_msg}")
                except:
                    QMessageBox.warning(self, "上传失败", f"HTTP错误: {response.status_code}")
                
                return None
                
        except Exception as e:
            print(f"原始API上传失败: {str(e)}")
            QMessageBox.critical(self, "上传失败", f"所有上传方式都失败: {str(e)}")
            return None

    def create_health_cert_api(self, name, gender, id_number, org_name, issue_date, valid_date):
        """调用API创建健康证 - 增强调试版"""
        try:
            # 显示进度
            self.generate_btn.setText("创建中...")
            self.generate_btn.setEnabled(False)
            
            # 添加进度条
            self.show_loading_progress("正在创建健康证...")
            
            # 检查认证状态
            print(f"=== API调用前检查 ===")
            print(f"Token有效性: {self.auth_manager.is_token_valid()}")
            print(f"Base URL: {self.auth_manager.base_url}")
            
            if not self.auth_manager.is_token_valid():
                self.hide_loading_progress()
                QMessageBox.warning(self, "认证失败", "访问令牌无效或已过期，请重新认证")
                return
            
            # 上传照片（如果有）
            photo_url = None
            if self.photo_path:
                self.update_loading_progress("正在上传照片...")
                photo_url = self.upload_photo()
                if photo_url is None and self.photo_path:  # 上传失败
                    self.hide_loading_progress()
                    self.generate_btn.setText("🏥 生成健康证")
                    self.generate_btn.setEnabled(True)
                    return
            
            # 准备API数据
            self.update_loading_progress("正在提交数据...")
            gender_value = 1 if gender == "男" else 0
            data = {
                "name": name,
                "id_number": id_number,
                "gender": gender_value,
                "org_name": org_name,
                "issue_date": issue_date,
                "valid_date": valid_date
            }
            
            if photo_url:
                data["photo_url"] = photo_url
            
            # 获取认证头
            auth_header = self.auth_manager.get_auth_header()
            if not auth_header:
                self.hide_loading_progress()
                QMessageBox.warning(self, "认证失败", "无法获取认证头，请重新认证")
                return
            
            # 调用创建健康证API
            url = self.auth_manager.certs_url  # 使用RESTful风格API
            headers = {"Content-Type": "application/json"}
            headers.update(auth_header)
            
            print(f"=== API请求详情 ===")
            print(f"请求URL: {url}")
            print(f"请求方法: POST")
            print(f"请求头: {headers}")
            print(f"请求数据: {data}")
            
            # 先测试网络连接
            try:
                test_response = requests.get(f"{self.auth_manager.api_base_url}/", timeout=5)
                print(f"网络连接测试: {test_response.status_code}")
            except Exception as e:
                print(f"网络连接测试失败: {e}")
                self.hide_loading_progress()
                QMessageBox.critical(self, "网络错误", f"无法连接到服务器：{str(e)}")
                return
            
            # 发送API请求
            response = requests.post(url, json=data, headers=headers, timeout=30)
            
            print(f"=== API响应详情 ===")
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")
            
            if response.status_code != 200:
                self.hide_loading_progress()
                QMessageBox.critical(self, "HTTP错误", f"服务器返回错误状态码: {response.status_code}\n响应内容: {response.text}")
                return
            
            try:
                result = response.json()
                print(f"解析后的JSON: {result}")
            except json.JSONDecodeError as e:
                self.hide_loading_progress()
                QMessageBox.critical(self, "响应解析失败", f"服务器响应格式错误：\n原始响应: {response.text}\n解析错误: {str(e)}")
                return
            
            if result.get("code") == 201:
                self.update_loading_progress("健康证创建成功，正在加载...")
                QMessageBox.information(self, "创建成功", "健康证创建成功！")
                
                # 获取健康证URL并异步跳转
                cert_data = result.get("data", {})
                cert_url = None
                
                print(f"健康证数据: {cert_data}")
                
                if cert_data.get("cert") and cert_data["cert"].get("view_url"):
                    cert_url = cert_data["cert"]["view_url"]
                elif cert_data.get("cert") and cert_data["cert"].get("cert_id"):
                    cert_id = cert_data["cert"]["cert_id"]
                    cert_url = self.get_cert_view_url(cert_id)
                else:
                    latest_cert = self.get_latest_cert()
                    if latest_cert and latest_cert.get("cert_id"):
                        cert_url = self.get_cert_view_url(latest_cert["cert_id"])
                
                print(f"健康证URL: {cert_url}")
                
                # 异步加载健康证页面
                if cert_url:
                    self.load_cert_async(cert_url)
                else:
                    self.hide_loading_progress()
                    if cert_data.get("cert"):
                        cert_info = cert_data["cert"]
                        info_msg = f"健康证创建成功！\n编号: {cert_info.get('cert_id', 'N/A')}\n姓名: {cert_info.get('name', 'N/A')}"
                        QMessageBox.information(self, "创建成功", info_msg)
                    
            else:
                self.hide_loading_progress()
                error_msg = result.get("message", "健康证创建失败")
                error_code = result.get("code", "未知")
                QMessageBox.warning(self, "创建失败", f"错误代码: {error_code}\n错误信息: {error_msg}")
                
        except requests.exceptions.Timeout as e:
            self.hide_loading_progress()
            QMessageBox.critical(self, "请求超时", f"请求超时，请检查网络连接：{str(e)}")
        except requests.exceptions.ConnectionError as e:
            self.hide_loading_progress()
            QMessageBox.critical(self, "连接错误", f"无法连接到服务器：{str(e)}")
        except requests.exceptions.RequestException as e:
            self.hide_loading_progress()
            QMessageBox.critical(self, "网络错误", f"网络请求失败：{str(e)}")
        except Exception as e:
            self.hide_loading_progress()
            QMessageBox.critical(self, "系统错误", f"创建健康证失败：{str(e)}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
        finally:
            self.generate_btn.setText("🏥 生成健康证")
            self.generate_btn.setEnabled(True)

    def get_cert_view_url(self, cert_id):
        """获取健康证查看URL"""
        try:
            auth_header = self.auth_manager.get_auth_header()
            if not auth_header:
                return None
            
            url = f"{self.auth_manager.cert_view_url}/{cert_id}"  # 使用RESTful风格API
            headers = {"Content-Type": "application/json"}
            headers.update(auth_header)
            
            response = requests.get(url, headers=headers, timeout=10)
            result = response.json()
            
            if result.get("code") == 200:
                cert_data = result.get("data", {})
                return cert_data.get("cert_url")
            else:
                print(f"获取健康证URL失败: {result.get('message')}")
                # 如果API失败，使用配置的基础URL构建
                return f"{self.auth_manager.api_base_url}/certs/view/{cert_id}"
                
        except Exception as e:
            print(f"获取健康证URL错误: {str(e)}")
            # 异常情况下使用配置的基础URL构建
            return f"{self.auth_manager.api_base_url}/certs/view/{cert_id}"

    def get_cert_list(self, page=1, limit=10, search=None):
        """获取健康证列表，支持分页和搜索"""
        try:
            auth_header = self.auth_manager.get_auth_header()
            if not auth_header:
                return None
            
            url = self.auth_manager.certs_url  # 使用RESTful风格API
            params = {
                "page": page,
                "limit": limit
            }
            
            # 添加搜索参数
            if search:
                params["search"] = search
            
            headers = {"Content-Type": "application/json"}
            headers.update(auth_header)
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            result = response.json()
            
            if result.get("code") == 200:
                return result.get("data", [])
            else:
                print(f"获取健康证列表失败: {result.get('message')}")
                return None
                
        except Exception as e:
            print(f"获取健康证列表错误: {str(e)}")
            return None

    def get_latest_cert(self):
        """获取最新创建的健康证"""
        try:
            # 获取第一页的第一条记录
            cert_list = self.get_cert_list(page=1, limit=1)
            if cert_list and len(cert_list) > 0:
                return cert_list[0]
            return None
            
        except Exception as e:
            print(f"获取最新健康证错误: {str(e)}")
            return None

    def view_cert_list(self):
        """查看健康证列表"""
        if not self.auth_manager.is_token_valid():
            QMessageBox.warning(self, "认证失败", "请先进行API认证")
            return
        
        try:
            # 获取健康证列表
            cert_list = self.get_cert_list(page=1, limit=20)  # 获取前20条记录
            
            if cert_list is None:
                QMessageBox.warning(self, "获取失败", "无法获取健康证列表")
                return
            
            if not cert_list:
                QMessageBox.information(self, "列表为空", "暂无健康证记录")
                return
            
            # 创建列表显示对话框
            self.show_cert_list_dialog(cert_list)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取健康证列表失败：{str(e)}")

    def show_cert_list_dialog(self, cert_list):
        """显示健康证列表对话框"""
        try:
            # 创建对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("健康证列表")
            dialog.setFixedSize(800, 600)
            dialog.setWindowModality(Qt.ApplicationModal)
            
            # 创建布局
            layout = QVBoxLayout(dialog)
            
            # 添加搜索框
            search_layout = QHBoxLayout()
            search_label = QLabel("搜索:")
            self.search_edit = QLineEdit()
            self.search_edit.setPlaceholderText("输入姓名或身份证号搜索...")
            search_btn = QPushButton("搜索")
            search_btn.clicked.connect(lambda: self.search_certs(dialog))
            
            search_layout.addWidget(search_label)
            search_layout.addWidget(self.search_edit)
            search_layout.addWidget(search_btn)
            layout.addLayout(search_layout)
            
            # 创建列表控件
            self.cert_list_widget = QListWidget()
            layout.addWidget(self.cert_list_widget)
            
            # 填充列表数据
            self.populate_cert_list(cert_list)
            
            # 添加按钮
            button_layout = QHBoxLayout()
            view_btn = QPushButton("查看选中")
            view_btn.clicked.connect(lambda: self.view_selected_cert(dialog))
            refresh_btn = QPushButton("刷新列表")
            refresh_btn.clicked.connect(lambda: self.refresh_cert_list(dialog))
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            
            button_layout.addWidget(view_btn)
            button_layout.addWidget(refresh_btn)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            layout.addLayout(button_layout)
            
            # 显示对话框
            dialog.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示健康证列表失败: {str(e)}")

    def populate_cert_list(self, cert_list):
        """填充健康证列表"""
        try:
            self.cert_list_widget.clear()
            
            for cert in cert_list:
                # 创建列表项
                item_text = f"姓名: {cert.get('name', 'N/A')} | "
                item_text += f"身份证: {cert.get('id_number', 'N/A')} | "
                item_text += f"体检日期: {cert.get('issue_date', 'N/A')} | "
                item_text += f"有效期: {cert.get('valid_date', 'N/A')}"
                
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, cert)  # 存储完整的证书数据
                
                self.cert_list_widget.addItem(item)
                
        except Exception as e:
            print(f"填充健康证列表失败: {e}")

    def refresh_cert_list(self, dialog):
        """刷新健康证列表"""
        try:
            # 重新获取列表
            cert_list = self.get_cert_list(page=1, limit=20)
            if cert_list is not None:
                self.populate_cert_list(cert_list)
                QMessageBox.information(dialog, "刷新成功", "健康证列表已更新")
            else:
                QMessageBox.warning(dialog, "刷新失败", "无法获取健康证列表")
        except Exception as e:
            QMessageBox.critical(dialog, "错误", f"刷新失败: {str(e)}")

    def upload_photo(self):
        """上传照片"""
        if not self.photo_path:
            return None
        
        try:
            print(f"开始上传照片: {self.photo_path}")
            
            # 首先尝试简化版上传
            filename = self.try_upload_simple()
            if filename:
                print(f"简化版上传成功: {filename}")
                return filename
            
            # 如果简化版失败，尝试原始上传
            filename = self.try_upload_original()
            if filename:
                print(f"原始上传成功: {filename}")
                return filename
            
            # 所有上传方式都失败
            QMessageBox.warning(self, "上传失败", "照片上传失败，将继续创建健康证（不含照片）")
            return None
            
        except Exception as e:
            print(f"上传照片异常: {str(e)}")
            QMessageBox.warning(self, "上传异常", f"照片上传异常: {str(e)}")
            return None

    def get_scaled_size(self, base_size):
        """根据窗口尺寸计算缩放后的尺寸"""
        return int(base_size * self.scale_factor)

    def get_scaled_font_size(self, base_size):
        """获取缩放后的字体大小"""
        return int(base_size * self.scale_factor)

    def get_left_panel_width(self):
        """获取左侧面板宽度"""
        # 计算50%的窗口宽度，但不少于200px
        calculated_width = int(self.window_width * 0.5)
        min_width = 200
        return max(calculated_width, min_width)

    def load_default_page(self):
        """加载默认页面"""
        try:
            current_browser = self.tabs.currentWidget()
            if current_browser:
                # 尝试加载一个简单的欢迎页面
                welcome_html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>健康证管理系统</title>
                    <style>
                        body {
                            font-family: 'Microsoft YaHei', Arial, sans-serif;
                            margin: 0;
                            padding: 40px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            text-align: center;
                        }
                        .container {
                            max-width: 600px;
                            margin: 0 auto;
                            background: rgba(255,255,255,0.1);
                            padding: 40px;
                            border-radius: 20px;
                            backdrop-filter: blur(10px);
                        }
                        h1 { font-size: 2.5em; margin-bottom: 20px; }
                        p { font-size: 1.2em; line-height: 1.6; }
                        .feature {
                            background: rgba(255,255,255,0.2);
                            padding: 20px;
                            margin: 20px 0;
                            border-radius: 10px;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🏥 健康证管理系统</h1>
                        <p>欢迎使用健康证管理系统！</p>
                        
                        <div class="feature">
                            <h3>📋 功能特色</h3>
                            <p>• 快速生成健康证<br>
                               • 在线查看和管理<br>
                               • 支持照片上传<br>
                               • 数据安全可靠</p>
                        </div>
                        
                        <div class="feature">
                            <h3>🚀 使用说明</h3>
                            <p>1. 点击左侧"初始化认证"按钮<br>
                               2. 填写健康证信息<br>
                               3. 点击"生成健康证"<br>
                               4. 查看生成的健康证</p>
                        </div>
                        
                        <p style="margin-top: 40px; opacity: 0.8;">
                            系统已就绪，请开始使用！
                        </p>
                    </div>
                </body>
                </html>
                """
                current_browser.setHtml(welcome_html)
                self.tabs.setTabText(0, "欢迎使用")
                self.urlbar.setText("健康证管理系统")
        except Exception as e:
            print(f"加载默认页面失败: {e}")

    def show_loading_progress(self, message):
        """显示加载进度"""
        try:
            # 创建进度对话框
            self.progress_dialog = QProgressDialog(message, "取消", 0, 0, self)
            self.progress_dialog.setWindowTitle("请稍候")
            self.progress_dialog.setWindowModality(Qt.WindowModal)
            self.progress_dialog.setAutoClose(False)
            self.progress_dialog.setAutoReset(False)
            self.progress_dialog.show()
            
            # 处理界面事件
            QApplication.processEvents()
        except Exception as e:
            print(f"显示进度对话框失败: {e}")

    def update_loading_progress(self, message):
        """更新加载进度"""
        try:
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.setLabelText(message)
                QApplication.processEvents()
        except Exception as e:
            print(f"更新进度对话框失败: {e}")

    def hide_loading_progress(self):
        """隐藏加载进度"""
        try:
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None
        except Exception as e:
            print(f"关闭进度对话框失败: {e}")

    def load_cert_async(self, cert_url):
        """异步加载健康证页面 - 优化版"""
        self.update_loading_progress("正在预热连接...")
        
        # 预热DNS和连接
        self.preheat_connection(cert_url)
        
        # 延迟加载以确保连接预热完成
        QTimer.singleShot(200, lambda: self.do_load_cert_optimized(cert_url))
    
    def preheat_connection(self, url):
        """预热网络连接"""
        try:
            import urllib.parse
            parsed_url = urllib.parse.urlparse(url)
            if parsed_url.netloc:
                # 简单的连接预热
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                try:
                    sock.connect((parsed_url.netloc.split(':')[0], 80))
                    sock.close()
                except:
                    pass
        except Exception as e:
            print(f"连接预热失败: {e}")

    def do_load_cert_optimized(self, cert_url):
        """执行优化的健康证页面加载"""
        try:
            self.update_loading_progress("正在加载健康证页面...")
            
            current_browser = self.tabs.currentWidget()
            if current_browser:
                # 设置加载策略
                current_browser.page().runJavaScript("""
                    // 优化图片加载
                    document.addEventListener('DOMContentLoaded', function() {
                        var images = document.querySelectorAll('img');
                        images.forEach(function(img) {
                            img.loading = 'eager';
                            img.decoding = 'sync';
                        });
                    });
                """)
                
                current_browser.load(QUrl(cert_url))
                self.urlbar.setText("健康证查看页面")
                
                # 监听加载完成
                current_browser.loadFinished.connect(self.on_cert_loaded_optimized)
            else:
                self.hide_loading_progress()
        except Exception as e:
            self.hide_loading_progress()
            QMessageBox.warning(self, "加载失败", f"无法加载健康证页面：{str(e)}")
    
    def on_cert_loaded_optimized(self, success):
        """优化的健康证页面加载完成处理"""
        if success:
            # 页面加载成功后，优化二维码显示
            current_browser = self.tabs.currentWidget()
            if current_browser:
                # 延迟执行JavaScript以确保页面完全加载
                QTimer.singleShot(1000, lambda: self.optimize_qr_code_display(current_browser))
        
        self.hide_loading_progress()
        if not success:
            QMessageBox.warning(self, "加载失败", "健康证页面加载失败，请点击刷新按钮重试")

    def optimize_qr_code_display(self, browser):
        """优化二维码显示"""
        try:
            # 注入JavaScript来优化二维码显示
            browser.page().runJavaScript("""
                // 查找并优化二维码图片
                var qrImages = document.querySelectorAll('img[src*="qr"], img[alt*="二维码"], img[alt*="QR"]');
                qrImages.forEach(function(img) {
                    img.style.imageRendering = 'pixelated';
                    img.style.imageRendering = '-moz-crisp-edges';
                    img.style.imageRendering = 'crisp-edges';
                    img.style.maxWidth = '200px';
                    img.style.height = 'auto';
                });
                
                // 优化页面显示
                document.body.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
            """)
        except Exception as e:
            print(f"优化二维码显示失败: {e}")

    def on_qr_code_check_complete(self, qr_count):
        """二维码检查完成回调"""
        if qr_count and qr_count > 0:
            print(f"检测到 {qr_count} 个二维码")
            self.show_temporary_message(f"页面加载完成，检测到 {qr_count} 个二维码")
        else:
            print("未检测到二维码")
            self.show_temporary_message("页面加载完成，如二维码未显示请点击刷新按钮")
    
    def refresh_current_page(self):
        """刷新当前页面"""
        try:
            current_browser = self.tabs.currentWidget()
            if current_browser:
                current_url = current_browser.url()
                if current_url.toString() and current_url.toString() != 'about:blank':
                    print(f"刷新页面: {current_url.toString()}")
                    current_browser.reload()
                    
                    # 显示刷新提示
                    self.show_temporary_message("正在刷新页面，请稍候...")
                else:
                    QMessageBox.information(self, "提示", "当前页面为空白页，无需刷新")
        except Exception as e:
            print(f"刷新页面失败: {e}")
            QMessageBox.warning(self, "刷新失败", f"页面刷新失败：{str(e)}")

    def show_temporary_message(self, message, duration=2000):
        """显示临时提示消息"""
        # 创建临时状态标签
        if not hasattr(self, 'temp_status_label'):
            self.temp_status_label = QLabel()
            self.temp_status_label.setStyleSheet(f"""
                QLabel {{
                    background-color: #333;
                    color: #fff;
                    padding: 10px;
                    border-radius: 5px;
                    font-size: {self.get_scaled_font_size(14)}px;
                    font-weight: bold;
                    opacity: 0.8;
                }}
            """)
            self.temp_status_label.setAlignment(Qt.AlignCenter)
            self.temp_status_label.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
            self.temp_status_label.setAttribute(Qt.WA_TranslucentBackground)
            self.temp_status_label.setFixedWidth(self.get_scaled_size(200))
            self.temp_status_label.setFixedHeight(self.get_scaled_size(50))
            self.temp_status_label.move(self.get_scaled_size(300), self.get_scaled_size(500))
            self.temp_status_label.show()
        
        # 更新临时状态标签的文本
        self.temp_status_label.setText(message)
        
        # 使用QTimer隐藏临时状态标签
        if hasattr(self, 'temp_status_timer'):
            self.temp_status_timer.stop()
        
        self.temp_status_timer = QTimer()
        self.temp_status_timer.setSingleShot(True)
        self.temp_status_timer.timeout.connect(self.temp_status_label.hide)
        self.temp_status_timer.start(duration)

    def create_left_panel_widget(self):
        """创建左侧控制面板"""
        # 创建左侧面板容器
        left_panel = QWidget()
        left_panel.setMinimumWidth(295)  # 增加宽度以更好显示卡密信息
        left_panel.setMaximumWidth(295)  # 设置最大宽度，防止过宽
        left_panel.setStyleSheet("""
            QWidget {
                background: #f8f9fa;
                border-radius: 15px;
                border: 1px solid #e9ecef;
            }
        """)

        # 创建主布局
        main_layout = QVBoxLayout(left_panel)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(0)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #f1f3f4;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #c1c8cd;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #a8b2b9;
            }
        """)

        # 创建滚动内容widget
        scroll_content = QWidget()
        scroll_content.setStyleSheet("background: transparent;")

        # 创建滚动内容布局
        left_layout = QVBoxLayout(scroll_content)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(8)
        
        # 添加标题
        title_label = QLabel("健康证管理系统")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                background: transparent;
                border: none;
                padding: 10px 0;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(title_label)

        # 添加用户信息卡片（卡密信息显示）
        self.create_user_info_card(left_layout)

        # 添加认证区域
        self.create_auth_section(left_layout)
        
        # 添加表单区域
        self.create_form_section(left_layout)
        
        # 添加操作按钮区域
        self.create_button_section(left_layout)
        
        # 添加认证状态显示
        self.auth_status = QLabel("⚠️ 未认证")
        self.auth_status.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                font-size: 12px;
                background: #fdf2f2;
                padding: 8px;
                border-radius: 6px;
                border-left: 3px solid #e74c3c;
            }
        """)
        self.auth_status.setWordWrap(True)
        left_layout.addWidget(self.auth_status)
        
        # 添加弹性空间
        left_layout.addStretch(1)

        # 将滚动内容设置到滚动区域
        scroll_area.setWidget(scroll_content)

        # 将滚动区域添加到主布局
        main_layout.addWidget(scroll_area)

        # 返回左侧面板widget
        return left_panel

    def create_user_info_card(self, layout):
        """创建用户信息卡片 - 紧凑版"""
        # 创建卡片容器
        card_widget = QWidget()
        card_widget.setFixedHeight(80)  # 减小高度
        card_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                margin: 2px 0;
            }
            QWidget:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7c8df0, stop:1 #8a5cb8);
            }
        """)

        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 3)
        card_widget.setGraphicsEffect(shadow)

        # 创建卡片布局 - 水平紧凑布局
        card_layout = QHBoxLayout(card_widget)
        card_layout.setContentsMargins(12, 8, 12, 8)
        card_layout.setSpacing(10)

        # 左侧：图标和类型
        left_layout = QVBoxLayout()
        left_layout.setSpacing(2)

        self.card_type_label = QLabel("🔒 未验证")
        self.card_type_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: bold;
                background: transparent;
                border: none;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }
        """)

        self.card_status_label = QLabel("等待验证")
        self.card_status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 10px;
                background: transparent;
                border: none;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)

        left_layout.addWidget(self.card_type_label)
        left_layout.addWidget(self.card_status_label)

        # 右侧：详细信息
        self.card_detail_label = QLabel("")
        self.card_detail_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 9px;
                background: transparent;
                border: none;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
            }
        """)
        self.card_detail_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.card_detail_label.setWordWrap(True)

        card_layout.addLayout(left_layout)
        card_layout.addStretch()
        card_layout.addWidget(self.card_detail_label)

        layout.addWidget(card_widget)

    def create_auth_section(self, layout):
        """创建认证区域"""
        # 认证按钮
        self.auth_btn = QPushButton("🔐 初始化认证")
        self.auth_btn.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
            QPushButton:pressed {
                background: #21618c;
            }
        """)
        self.auth_btn.clicked.connect(self.initialize_auth)
        layout.addWidget(self.auth_btn)

    def create_form_section(self, layout):
        """创建表单区域 - 紧凑版"""
        # 创建简洁的表单容器
        form_container = QWidget()
        form_container.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)

        # 创建表单布局
        form_layout = QVBoxLayout(form_container)
        form_layout.setContentsMargins(12, 12, 12, 12)
        form_layout.setSpacing(8)

        # 简洁的表单标题
        form_title = QLabel("📋 健康证基本信息")
        form_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                background: transparent;
                border: none;
                padding: 0 0 8px 0;
            }
        """)
        form_layout.addWidget(form_title)
        
        # 个人信息分组标题
        personal_title = QLabel("👤 个人信息")
        personal_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                background: transparent;
                border: none;
                padding: 8px 0 4px 0;
            }
        """)
        form_layout.addWidget(personal_title)

        # 姓名和性别 - 水平布局
        name_gender_layout = QHBoxLayout()
        name_gender_layout.setSpacing(10)

        # 姓名输入
        name_label = QLabel("姓名 *")
        name_label.setStyleSheet(self.get_compact_label_style())
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入真实姓名")
        self.name_edit.setStyleSheet(self.get_compact_input_style())

        name_layout = QVBoxLayout()
        name_layout.setSpacing(2)
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_edit)

        # 性别选择
        gender_label = QLabel("性别 *")
        gender_label.setStyleSheet(self.get_compact_label_style())
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["男", "女"])
        self.gender_combo.setStyleSheet(self.get_compact_combo_style())

        gender_layout = QVBoxLayout()
        gender_layout.setSpacing(2)
        gender_layout.addWidget(gender_label)
        gender_layout.addWidget(self.gender_combo)

        name_gender_layout.addLayout(name_layout, 2)
        name_gender_layout.addLayout(gender_layout, 1)
        form_layout.addLayout(name_gender_layout)

        # 身份证号
        id_label = QLabel("身份证号 *")
        id_label.setStyleSheet(self.get_compact_label_style())
        self.id_edit = QLineEdit()
        self.id_edit.setPlaceholderText("请输入18位身份证号")
        self.id_edit.setStyleSheet(self.get_compact_input_style())
        form_layout.addWidget(id_label)
        form_layout.addWidget(self.id_edit)
        
        # 体检信息分组标题
        exam_title = QLabel("🏥 体检信息")
        exam_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                background: transparent;
                border: none;
                padding: 12px 0 4px 0;
            }
        """)
        form_layout.addWidget(exam_title)

        # 体检单位
        unit_label = QLabel("体检单位 *")
        unit_label.setStyleSheet(self.get_compact_label_style())
        self.unit_edit = QLineEdit()
        self.unit_edit.setPlaceholderText("请输入体检单位名称")
        self.unit_edit.setStyleSheet(self.get_compact_input_style())
        form_layout.addWidget(unit_label)
        form_layout.addWidget(self.unit_edit)

        # 日期选择 - 水平布局
        date_layout = QHBoxLayout()
        date_layout.setSpacing(10)

        # 体检日期
        exam_date_label = QLabel("体检日期 *")
        exam_date_label.setStyleSheet(self.get_compact_label_style())
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setStyleSheet(self.get_compact_date_style())

        exam_date_layout = QVBoxLayout()
        exam_date_layout.setSpacing(2)
        exam_date_layout.addWidget(exam_date_label)
        exam_date_layout.addWidget(self.date_edit)

        # 有效期至
        expiry_label = QLabel("有效期至 *")
        expiry_label.setStyleSheet(self.get_compact_label_style())
        self.expiry_edit = QDateEdit()
        self.expiry_edit.setDate(QDate.currentDate().addYears(1))
        self.expiry_edit.setCalendarPopup(True)
        self.expiry_edit.setStyleSheet(self.get_compact_date_style())

        expiry_date_layout = QVBoxLayout()
        expiry_date_layout.setSpacing(2)
        expiry_date_layout.addWidget(expiry_label)
        expiry_date_layout.addWidget(self.expiry_edit)

        date_layout.addLayout(exam_date_layout)
        date_layout.addLayout(expiry_date_layout)
        form_layout.addLayout(date_layout)

        # 照片上传分组标题
        photo_title = QLabel("📷 照片上传")
        photo_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                background: transparent;
                border: none;
                padding: 12px 0 4px 0;
            }
        """)
        form_layout.addWidget(photo_title)

        # 创建紧凑的拖拽上传区域
        self.photo_upload_area = self.create_compact_photo_upload_area()
        form_layout.addWidget(self.photo_upload_area)

        # 初始化照片路径
        self.photo_path = None

        # 将表单容器添加到主布局
        layout.addWidget(form_container)

    def create_form_group(self, title, parent_layout):
        """创建表单分组"""
        # 分组标题
        group_title = QLabel(title)
        group_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 600;
                color: #495057;
                background: transparent;
                border: none;
                padding: 15px 0 8px 0;
            }
        """)
        parent_layout.addWidget(group_title)

        # 创建分组容器
        group_container = QWidget()
        group_container.setStyleSheet("""
            QWidget {
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)

        group_layout = QVBoxLayout(group_container)
        group_layout.setContentsMargins(15, 15, 15, 15)
        group_layout.setSpacing(10)

        parent_layout.addWidget(group_container)
        return group_layout

    def get_compact_label_style(self):
        """获取紧凑标签样式"""
        return """
            QLabel {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                background: transparent;
                border: none;
                padding: 0;
                margin: 0;
            }
        """

    def get_compact_input_style(self):
        """获取紧凑输入框样式"""
        return """
            QLineEdit {
                padding: 8px 10px;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background: white;
                color: #495057;
                selection-background-color: #007bff;
            }
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
                background: #fff;
            }
            QLineEdit:hover {
                border-color: #ced4da;
            }
        """

    def get_compact_combo_style(self):
        """获取紧凑下拉框样式"""
        return """
            QComboBox {
                padding: 8px 10px;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background: white;
                color: #495057;
                min-width: 80px;
            }
            QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QComboBox:hover {
                border-color: #ced4da;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-right: 5px;
            }
        """

    def get_compact_date_style(self):
        """获取紧凑日期选择器样式"""
        return """
            QDateEdit {
                padding: 8px 10px;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background: white;
                color: #495057;
            }
            QDateEdit:focus {
                border-color: #007bff;
                outline: none;
            }
            QDateEdit:hover {
                border-color: #ced4da;
            }
            QDateEdit::drop-down {
                border: none;
                width: 20px;
            }
            QDateEdit::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-right: 5px;
            }
        """

    def get_label_style(self):
        """获取标签样式"""
        return """
            QLabel {
                font-size: 13px;
                font-weight: 600;
                color: #495057;
                background: transparent;
                border: none;
                margin-bottom: 5px;
            }
        """

    def get_input_style(self):
        """获取输入框样式"""
        return """
            QLineEdit {
                padding: 12px 15px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                color: #495057;
                selection-background-color: #007bff;
            }
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
                background: #fff;
            }
            QLineEdit:hover {
                border-color: #ced4da;
            }
        """

    def get_combo_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                padding: 12px 15px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                color: #495057;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QComboBox:hover {
                border-color: #ced4da;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-right: 10px;
            }
        """

    def get_date_style(self):
        """获取日期选择器样式"""
        return """
            QDateEdit {
                padding: 12px 15px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                color: #495057;
            }
            QDateEdit:focus {
                border-color: #007bff;
                outline: none;
            }
            QDateEdit:hover {
                border-color: #ced4da;
            }
            QDateEdit::drop-down {
                border: none;
                width: 30px;
            }
            QDateEdit::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-right: 10px;
            }
        """

    def create_compact_photo_upload_area(self):
        """创建紧凑的照片上传区域"""
        upload_container = QWidget()
        upload_container.setStyleSheet("""
            QWidget {
                background: white;
                border: 1px dashed #ced4da;
                border-radius: 6px;
                min-height: 60px;
                max-height: 60px;
            }
            QWidget:hover {
                border-color: #007bff;
                background: #f8f9ff;
            }
        """)

        upload_layout = QHBoxLayout(upload_container)
        upload_layout.setContentsMargins(10, 5, 10, 5)
        upload_layout.setSpacing(8)
        upload_layout.setAlignment(Qt.AlignCenter)

        # 上传图标
        upload_icon = QLabel("📷")
        upload_icon.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #6c757d;
                background: transparent;
                border: none;
            }
        """)
        upload_layout.addWidget(upload_icon)

        # 上传提示文字
        upload_text = QLabel("点击选择或拖拽照片")
        upload_text.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                background: transparent;
                border: none;
            }
        """)
        upload_layout.addWidget(upload_text)

        # 文件信息显示
        self.photo_info = QLabel("未选择")
        self.photo_info.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #28a745;
                background: transparent;
                border: none;
            }
        """)
        upload_layout.addWidget(self.photo_info)

        # 设置点击事件 - 使用lambda包装以避免事件处理异常
        upload_container.mousePressEvent = lambda event: self.handle_photo_click(event)

        # 启用拖拽功能
        upload_container.setAcceptDrops(True)
        upload_container.dragEnterEvent = self.drag_enter_event
        upload_container.dropEvent = self.drop_event

        return upload_container

    def create_photo_upload_area(self):
        """创建照片上传区域"""
        upload_container = QWidget()
        upload_container.setStyleSheet("""
            QWidget {
                background: white;
                border: 2px dashed #ced4da;
                border-radius: 8px;
                min-height: 120px;
            }
            QWidget:hover {
                border-color: #007bff;
                background: #f8f9ff;
            }
        """)

        upload_layout = QVBoxLayout(upload_container)
        upload_layout.setContentsMargins(20, 20, 20, 20)
        upload_layout.setSpacing(10)
        upload_layout.setAlignment(Qt.AlignCenter)

        # 上传图标
        upload_icon = QLabel("📷")
        upload_icon.setStyleSheet("""
            QLabel {
                font-size: 32px;
                color: #6c757d;
                background: transparent;
                border: none;
            }
        """)
        upload_icon.setAlignment(Qt.AlignCenter)
        upload_layout.addWidget(upload_icon)

        # 上传提示文字
        upload_text = QLabel("点击选择照片或拖拽文件到此处")
        upload_text.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                background: transparent;
                border: none;
                font-weight: 500;
            }
        """)
        upload_text.setAlignment(Qt.AlignCenter)
        upload_layout.addWidget(upload_text)

        # 文件格式提示
        format_text = QLabel("支持 JPG、PNG、GIF 格式，最大 2MB")
        format_text.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #adb5bd;
                background: transparent;
                border: none;
            }
        """)
        format_text.setAlignment(Qt.AlignCenter)
        upload_layout.addWidget(format_text)

        # 选择按钮
        self.photo_btn = QPushButton("📁 选择文件")
        self.photo_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
            QPushButton:pressed {
                background: #004085;
            }
        """)
        self.photo_btn.clicked.connect(self.select_photo)
        upload_layout.addWidget(self.photo_btn)

        # 文件信息显示
        self.photo_info = QLabel("未选择文件")
        self.photo_info.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                background: transparent;
                border: none;
                padding: 5px 0;
            }
        """)
        self.photo_info.setAlignment(Qt.AlignCenter)
        self.photo_info.setWordWrap(True)
        upload_layout.addWidget(self.photo_info)

        # 启用拖拽功能
        upload_container.setAcceptDrops(True)
        upload_container.dragEnterEvent = self.drag_enter_event
        upload_container.dropEvent = self.drop_event

        return upload_container

    def drag_enter_event(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否为支持的图片文件
            urls = event.mimeData().urls()
            if urls:
                file_path = urls[0].toLocalFile()
                if file_path.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                    event.accept()
                    return
        event.ignore()

    def drop_event(self, event):
        """拖拽放下事件"""
        try:
            files = [u.toLocalFile() for u in event.mimeData().urls()]
            if files:
                file_path = files[0]

                # 检查文件是否存在
                if not os.path.exists(file_path):
                    QMessageBox.warning(self, "文件错误", "拖拽的文件不存在")
                    return

                # 检查文件类型 - 与服务器要求保持一致
                file_ext = os.path.splitext(file_path)[1].lower()
                allowed_formats = ['.jpg', '.png', '.gif']  # 与服务器支持的格式一致
                if file_ext not in allowed_formats:
                    QMessageBox.warning(self, "格式不支持", f"不支持的文件格式: {file_ext}\n服务器只支持JPG、PNG或GIF格式")
                    return

                # 检查文件大小
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # 转换为MB
                if file_size > 2:
                    QMessageBox.warning(self, "文件过大", f"文件大小 {file_size:.1f}MB 超过2MB限制")
                    return

                # 文件验证通过，设置照片路径
                self.photo_path = file_path
                file_info = os.path.basename(file_path)
                self.photo_info.setText(f"✅ {file_info}")
                self.photo_info.setStyleSheet("""
                    QLabel {
                        font-size: 11px;
                        color: #28a745;
                        background: transparent;
                        border: none;
                        padding: 5px 0;
                    }
                """)

                print(f"✓ 拖拽上传成功: {file_info} ({file_size:.1f}MB)")

        except Exception as e:
            print(f"拖拽上传失败: {e}")
            QMessageBox.critical(self, "拖拽上传失败", f"处理拖拽文件时发生错误：{str(e)}")
            # 重置照片路径
            self.photo_path = None
            if hasattr(self, 'photo_info'):
                self.photo_info.setText("未选择")

    def create_button_section(self, layout):
        """创建操作按钮区域"""
        # 生成健康证按钮
        self.generate_btn = QPushButton("🏥 生成健康证")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #229954;
            }
            QPushButton:pressed {
                background: #1e8449;
            }
        """)
        self.generate_btn.clicked.connect(self.generate_health_cert)
        layout.addWidget(self.generate_btn)

        # 保存按钮
        self.save_btn = QPushButton("💾 保存健康证")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background: #9b59b6;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #8e44ad;
            }
            QPushButton:pressed {
                background: #7d3c98;
            }
        """)
        self.save_btn.clicked.connect(self.save_health_cert)
        layout.addWidget(self.save_btn)

    def create_browser_area_widget(self):
        """创建右侧浏览器区域"""
        # 创建右侧面板
        right_panel = QWidget()
        right_panel.setMinimumWidth(465)  # 设置最小宽度以容纳445px浏览器
        right_panel.setMaximumWidth(465)  # 设置最大宽度固定尺寸
        right_panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 15px;
                border: 1px solid #e9ecef;
            }
        """)

        # 创建右侧布局
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(10, 10, 10, 10)
        right_layout.setSpacing(5)
        
        # 创建浏览器工具栏 - 已隐藏
        # browser_toolbar = self.create_browser_toolbar()

        # 创建虚拟的urlbar对象以避免引用错误
        self.urlbar = QLineEdit()
        self.urlbar.hide()  # 隐藏但保持对象存在

        # 创建标签页控件
        self.tabs = QTabWidget()
        self.tabs.setTabsClosable(True)
        self.tabs.tabCloseRequested.connect(self.close_tab)
        self.tabs.currentChanged.connect(self.on_tab_changed)

        # 设置标签页样式
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 8px;
                background: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 1px solid #ddd;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom-color: white;
            }
            QTabBar::tab:hover {
                background: #e9ecef;
            }
        """)

        # 隐藏标签栏，只显示浏览器内容
        self.tabs.tabBar().hide()

        # 将标签页直接添加到右侧布局（工具栏已隐藏）
        # right_layout.addLayout(browser_toolbar)  # 已注释掉
        right_layout.addWidget(self.tabs)

        # 添加初始标签页
        self.add_initial_tab()

        # 返回右侧面板widget
        return right_panel

    def create_browser_toolbar(self):
        """创建浏览器工具栏"""
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setContentsMargins(5, 5, 5, 5)
        
        # 后退按钮
        self.back_btn = QPushButton("←")
        self.back_btn.setFixedSize(30, 30)
        self.back_btn.clicked.connect(self.go_back)
        toolbar_layout.addWidget(self.back_btn)
        
        # 前进按钮
        self.forward_btn = QPushButton("→")
        self.forward_btn.setFixedSize(30, 30)
        self.forward_btn.clicked.connect(self.go_forward)
        toolbar_layout.addWidget(self.forward_btn)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("⟳")
        self.refresh_btn.setFixedSize(30, 30)
        self.refresh_btn.clicked.connect(self.refresh_page)
        toolbar_layout.addWidget(self.refresh_btn)
        
        # URL地址栏
        self.urlbar = QLineEdit()
        self.urlbar.setPlaceholderText("输入网址或搜索...")
        self.urlbar.returnPressed.connect(self.navigate_to_url)
        toolbar_layout.addWidget(self.urlbar)
        
        # 新标签页按钮
        self.new_tab_btn = QPushButton("+")
        self.new_tab_btn.setFixedSize(30, 30)
        self.new_tab_btn.clicked.connect(lambda: self.add_new_tab())
        toolbar_layout.addWidget(self.new_tab_btn)
        
        return toolbar_layout

    def add_initial_tab(self):
        """添加初始标签页"""
        # 添加初始标签页 - 使用本地欢迎页面
        welcome_path = os.path.join(os.path.dirname(__file__), 'welcome.html')
        if os.path.exists(welcome_path):
            welcome_url = QUrl.fromLocalFile(os.path.abspath(welcome_path))
            self.add_new_tab(welcome_url, '欢迎页面')
        else:
            # 如果本地文件不存在，延迟加载网络页面
            self.add_new_tab(QUrl('about:blank'), '加载中...')
            QTimer.singleShot(2000, self.load_default_page)

    def go_back(self):
        """后退"""
        current_browser = self.tabs.currentWidget()
        if current_browser:
            current_browser.back()

    def go_forward(self):
        """前进"""
        current_browser = self.tabs.currentWidget()
        if current_browser:
            current_browser.forward()

    def refresh_page(self):
        """刷新页面"""
        current_browser = self.tabs.currentWidget()
        if current_browser:
            current_browser.reload()

    def navigate_to_url(self):
        """导航到URL"""
        url = self.urlbar.text()
        if url:
            if not url.startswith(('http://', 'https://')):
                url = 'http://' + url
            current_browser = self.tabs.currentWidget()
            if current_browser:
                current_browser.load(QUrl(url))

    def close_tab(self, index):
        """关闭标签页"""
        if self.tabs.count() > 1:
            self.tabs.removeTab(index)
        else:
            # 如果只有一个标签页，创建新的空白页
            self.add_new_tab()
            self.tabs.removeTab(index)

    def on_tab_changed(self, index):
        """标签页切换事件"""
        current_browser = self.tabs.currentWidget()
        if current_browser:
            url = current_browser.url()
            # 使用统一的URL隐藏逻辑
            self.renew_urlbar(url, current_browser)

    def cleanup_memory(self):
        """清理内存"""
        try:
            # 清理不活跃的标签页缓存
            for i in range(self.tabs.count()):
                browser = self.tabs.widget(i)
                if browser and i != self.tabs.currentIndex():
                    # 对非当前标签页进行轻量级清理
                    browser.page().runJavaScript("if(window.gc) window.gc();")
        except Exception as e:
            print(f"内存清理失败: {e}")

    def auto_refresh_page(self):
        """自动刷新页面"""
        if self.is_auto_refresh_enabled:
            current_browser = self.tabs.currentWidget()
            if current_browser:
                current_browser.reload()


if __name__ == '__main__':
    # 设置WebEngine优化参数 - 平衡性能和功能
    chromium_flags = [
        '--disable-gpu',
        '--disable-software-rasterizer', 
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-extensions',
        '--disable-sync',
        '--disable-translate',
        '--disable-plugins',
        '--disable-plugins-discovery',
        '--disable-print-preview',
        '--disable-default-apps',
        '--disable-background-mode',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-component-extensions-with-background-pages',
        '--disable-dev-shm-usage',
        '--memory-pressure-off',
        '--max_old_space_size=512',
        '--disable-logging',
        '--enable-fast-unload',
        '--disable-extensions-http-throttling',
        '--disable-gpu-rasterization',
        '--disable-threaded-animation',
        '--disable-checker-imaging',
        '--disable-partial-raster',
        '--disable-smooth-scrolling',
        '--disable-accelerated-2d-canvas',
        '--disable-accelerated-jpeg-decoding',
        '--disable-accelerated-mjpeg-decode',
        '--disable-accelerated-video-decode',
        '--num-raster-threads=1',
        '--enable-simple-cache-backend'
    ]
    
    os.environ['QTWEBENGINE_CHROMIUM_FLAGS'] = ' '.join(chromium_flags)
    
    my_application = QApplication(sys.argv)
    
    # 设置应用程序属性以优化性能
    my_application.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)
    my_application.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)
    my_application.setAttribute(Qt.AA_UseHighDpiPixmaps, False)
    
    main_demo = MainDemo()
    main_demo.show()
    my_application.exec_()
