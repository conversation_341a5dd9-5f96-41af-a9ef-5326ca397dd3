import hashlib
import uuid
import platform
import psutil
import json
import os
from datetime import datetime

class DeviceManager:
    """设备ID管理器"""
    
    def __init__(self, config_file="device_config.json"):
        self.config_file = config_file
        self.device_id = None
        self.device_cache = {}

        # 导入配置管理器
        from config_manager import ConfigManager
        self.config_manager = ConfigManager()

        self.load_or_generate_device_id()
    
    def get_hardware_info(self):
        """获取硬件信息"""
        try:
            # 获取MAC地址
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0,2*6,2)][::-1])
            
            # 获取CPU信息
            cpu_info = platform.processor()
            
            # 获取系统信息
            system_info = f"{platform.system()}-{platform.release()}"
            
            # 获取内存信息
            memory_info = str(psutil.virtual_memory().total)
            
            return f"{mac}-{cpu_info}-{system_info}-{memory_info}"
        except:
            # 如果获取硬件信息失败，使用UUID
            return str(uuid.uuid4())
    
    def generate_device_id(self):
        """生成设备ID"""
        hardware_info = self.get_hardware_info()
        device_id = hashlib.md5(hardware_info.encode()).hexdigest()
        return device_id
    
    def load_or_generate_device_id(self):
        """加载或生成设备ID - 优化缓存策略"""
        try:
            # 首先尝试从配置管理器加载
            device_config = self.config_manager.get_config('device_config')
            cached_device_id = device_config.get('device_id')

            if cached_device_id and self.is_device_id_valid(cached_device_id, device_config):
                self.device_id = cached_device_id
                print(f"✓ 使用缓存的设备ID: {self.get_formatted_device_id()}")
                return

            # 兼容性：尝试从原始文件加载
            if os.path.exists(self.config_file):
                try:
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        legacy_device_id = config.get('device_id')
                        if legacy_device_id and self.is_device_id_valid(legacy_device_id, config):
                            self.device_id = legacy_device_id
                            # 迁移到配置管理器
                            self.save_device_config()
                            print(f"✓ 迁移设备ID到新配置系统: {self.get_formatted_device_id()}")
                            return
                except Exception as e:
                    print(f"加载旧设备配置失败: {e}")

            # 生成新的设备ID
            print("生成新的设备ID...")
            self.device_id = self.generate_device_id()
            self.save_device_config()
            print(f"✓ 生成新设备ID: {self.get_formatted_device_id()}")

        except Exception as e:
            print(f"设备ID加载失败: {e}")
            # 回退到简单生成
            self.device_id = self.generate_device_id()

    def is_device_id_valid(self, device_id, config):
        """验证设备ID是否有效"""
        try:
            # 检查设备ID格式
            if not device_id or len(device_id) != 32:
                return False

            # 检查缓存时间
            cache_duration = self.config_manager.get_config('device_config', 'cache_duration', 86400)
            generated_at = config.get('generated_at')

            if generated_at:
                from datetime import datetime
                generated_time = datetime.fromisoformat(generated_at)
                current_time = datetime.now()
                age_seconds = (current_time - generated_time).total_seconds()

                if age_seconds > cache_duration:
                    print(f"设备ID已过期 (年龄: {age_seconds/3600:.1f}小时)")
                    return False

            # 验证设备ID是否与当前硬件匹配
            current_hardware_id = self.generate_device_id()
            if device_id != current_hardware_id:
                print("设备硬件已变更，需要重新生成设备ID")
                return False

            return True

        except Exception as e:
            print(f"设备ID验证失败: {e}")
            return False
    
    def save_device_config(self):
        """保存设备配置 - 优化版"""
        config = {
            'device_id': self.device_id,
            'generated_at': datetime.now().isoformat(),
            'platform': platform.system(),
            'hostname': platform.node(),
            'hardware_hash': self.get_hardware_hash()
        }

        try:
            # 保存到配置管理器
            for key, value in config.items():
                self.config_manager.set_config('device_config', key, value)

            # 兼容性：同时保存到原始文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            print("✓ 设备配置保存成功")

        except Exception as e:
            print(f"保存设备配置失败: {e}")

    def get_hardware_hash(self):
        """获取硬件哈希值用于验证"""
        try:
            hardware_info = self.get_hardware_info()
            import hashlib
            return hashlib.sha256(hardware_info.encode()).hexdigest()[:16]
        except:
            return "unknown"
    
    def get_device_id(self):
        """获取设备ID"""
        return self.device_id
    
    def get_formatted_device_id(self):
        """获取格式化的设备ID（用于显示）"""
        if self.device_id:
            # 每4个字符添加一个空格，便于阅读
            formatted = '-'.join([self.device_id[i:i+4] for i in range(0, len(self.device_id), 4)])
            return formatted.upper()
        return "未知设备"