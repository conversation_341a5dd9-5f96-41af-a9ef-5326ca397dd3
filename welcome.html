<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康证管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2em;
            font-weight: 300;
        }
        
        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        
        .status {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 30px;
        }
        
        .action-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏥</div>
        <h1>健康证管理系统</h1>
        <p class="subtitle">专业、安全、高效的健康证明管理平台</p>
        
        <div class="status">
            <strong>✅ 系统已就绪</strong><br>
            请使用左侧表单创建健康证，或点击下方快捷操作
        </div>
        
        <div class="quick-actions">
            <button class="action-btn" onclick="loadSystemPage()">🏠 系统首页</button>
            <button class="action-btn" onclick="loadMobilePage()">📱 手机版</button>
        </div>
    </div>
    
    <script>
        function loadSystemPage() {
            window.location.href = '';
        }
        
        function loadMobilePage() {
            window.location.href = '';
        }
        
        // 自动检测网络连接
        setTimeout(() => {
            fetch('http://**************/zm.html', {method: 'HEAD', mode: 'no-cors'})
                .then(() => {
                    document.querySelector('.status').innerHTML = 
                        '<strong>✅ 网络连接正常</strong><br>可以正常访问健康证系统';
                })
                .catch(() => {
                    document.querySelector('.status').innerHTML = 
                        '<strong>⚠️ 网络连接异常</strong><br>请检查网络连接或服务器状态';
                    document.querySelector('.status').style.background = '#fff3cd';
                    document.querySelector('.status').style.color = '#856404';
                    document.querySelector('.status').style.borderLeftColor = '#ffc107';
                });
        }, 1000);
    </script>
</body>
</html>